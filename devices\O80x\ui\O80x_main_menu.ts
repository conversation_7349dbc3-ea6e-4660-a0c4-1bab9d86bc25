import ble                 from '../../../BLE/ble';

import * as bsh_ble_comm   from '../../../Bork_Smart_Home_BLE/BSH_BLE_communications';
import * as ui             from '../../../UI/ui_common_logic';
import * as submenu        from './O80x_commands_submenu';
import * as telem_parser   from '../O80x_telemetry_parser';
import * as utils          from '../../utils';
import * as replies_parser from '../../../Bork_Smart_Home_BLE/BSH_BLE_replies';



interface LineItem {
    text: string;
    action: (() => Promise<void>) | null;
}

interface MainMenu {
    line_items: LineItem[];
}



import {
    DEVICE_NAME,
    DEVICE_MAC,
    DEVICE_UUID,
    BSH_CONTROL_SERVICE,
    BSH_CHAR_DEVICE_TYPE,
    BSH_CHAR_FIRM_VER,
    BSH_WRITE_CHAR,
    BSH_READ_CHAR,
    BSH_CHAR_STATUS,
    BSH_CHAR_TELEM,
    BLE_SCAN_TIME,
} from '../O80x_config';

const logContent = ui.logContent;



const main_menu: MainMenu = {
    line_items: [{text: '   --- CONNECT ---   ',                                action: null},
                 {text: 'Connect to O80x by hardcoded MAC (uuid for apple))',   action: connect_by_mac},
                 {text: 'Connect to nearest O80x',                              action: connect_nearest},
                 {text: 'Connect - pick from list',                             action: connect_to_device_from_list},
                 {text: 'Disconnect',                                           action: ble.main.disconnect},
                 {text: '',                                                     action: null},
                 {text: '   --- COMMANDS ---   ',                               action: null},
                 {text: 'Control commands ►',                                   action: submenu_action},
                 {text: '    - wifi -   ',                                      action: null},
                 {text: 'Set Wifi SSID',                                        action: set_ssid},
                 {text: 'Set WiFi Password',                                    action: set_pswd},
                 {text: 'Connect to WiFi',                                      action: connect_to_wifi},
                 {text: '    - ota -   ',                                       action: null},
                 {text: 'Update firmware(OTA)',                                 action: ota_run},
                 {text: 'OTA using custom link',                                action: ota_via_custom_link},
                 {text: '    - balancer -   ',                                  action: null},
                 {text: 'Set balancer link',                                    action: set_balancer_link},
                 {text: 'Get broker from balancer',                             action: get_broker_link},
                 {text: '    - broker -   ',                                    action: null},
                 {text: 'Set broker ID',                                        action: set_broker_id},
                 {text: 'Set broker password',                                  action: set_broker_pswd},
                 {text: 'Connect to broker',                                    action: connect_to_broker},
                 {text: '    - read info chars -   ',                           action: null},
                 {text: 'Read device type from F301',                           action: read_device_type},
                 {text: 'Read firmware version from F302',                      action: read_firmware_version},
                 {text: '',                                                     action: null},
                 {text: '   --- UTILS ---',                                     action: null},
                 {text: 'Scan BLE',                                             action: scan_ble},
                 {text: 'Send raw data',                                        action: utils.send_raw_data},
                 {text: 'Clear logs',                                           action: ui.clear_logs},
                 {text: 'Scan services and chars (connect first ^^)',           action: ble.main.discover_services_and_chars}]
};



function init(): void
{
    ui.init(get_main_menu_items_list(), submenu.get_submenu_items_list(), treat_keys_callback);
    submenu.init(closing_submenu_callback);
    init_keys_action();
    ui.ui_elements.main_menu_list.focus();
}



/*****************************
 *    action keys
 ****************************/
function init_keys_action() : void {
    ui.ui_elements.main_menu_list.on('select', async function (selected_item: any) 
    {
        const itm = main_menu.line_items.find(line => line.text === selected_item.content);
        if (!itm)
        {
            throw new Error(`Action not found for item: ${selected_item.content}`);
        }

        if (itm.action === null)
        {
            return;
        }

        if (typeof itm.action === 'function') {
            await itm.action();
        }
    });
}



function treat_keys_callback(active_window: any, key: string): void
{
    if((active_window === ui.ui_elements.main_menu_list || active_window === ui.ui_elements.logsBox) && key === 'escape')
    {
        const conn_status = ble.main.get_connection_status();
        logContent("ble status: ".concat(conn_status));
        if(conn_status === 'connecting')
        {
            ble.main.stop_scanning();
            logContent("stopping scanning...");
        } else if(conn_status === "connected") {
            ble.main.disconnect();
            logContent("disconnected");
        } else {
            return process.exit(0);
        }
    }
}



/************************************
 *    main menu items actions
 ***********************************/
async function connect_by_mac(): Promise<void> 
{
    logContent('поиск O80x...');
    await utils.connect_by_mac(DEVICE_MAC, DEVICE_UUID, BSH_WRITE_CHAR, BSH_READ_CHAR, data_from_device_cb);
    after_connect();
}

async function connect_nearest(): Promise<void> {
    logContent('Поиск ближайшего O80x...');
    await utils.connect_to_nearest(DEVICE_NAME, null, BSH_WRITE_CHAR, BSH_READ_CHAR, data_from_device_cb);
    after_connect();
}

async function connect_to_device_from_list(): Promise<void>
{
    logContent('Поиск всех O80x...');
    await utils.pick_from_list_and_connect(DEVICE_NAME, null, BSH_WRITE_CHAR, BSH_READ_CHAR, data_from_device_cb)
    await after_connect();
}



async function after_connect(): Promise<void>
{
    bsh_ble_comm.utils.enable_logs(logContent);

    if (ble.main.get_connection_status() != 'connected') 
    {
        // logContent('err: not connected. status: '.concat(ble.main.get_connection_status()));
        return;
    }

    await ble.main.subscribe_to(BSH_CHAR_TELEM, telemetry_cb);
    await ble.main.subscribe_to(BSH_CHAR_STATUS, status_data_cb);
    
    await ble.utils.delay(2000);

    
    await bsh_ble_comm.device_info_getters.get_model();
    await bsh_ble_comm.device_info_getters.get_firmware_version();
    await bsh_ble_comm.device_info_getters.get_device_id();

    return;
}



function submenu_action(): Promise<void> {
    ui.ui_elements.main_menu_list.hide();
    ui.ui_elements.submenu_list.show();
    ui.ui_elements.submenu_list.focus();
    ui.ui_elements.screen.render();
    return;
}



async function scan_ble(): Promise<void>
{
    ble.main.scan_all(BLE_SCAN_TIME);
}



/************************************
 *    Bork smart home BLE protocol commands
 ***********************************/
async function set_ssid(): Promise<void>
{
    logContent('Setting wifi ssid');
    if(ble.main.get_connection_status() === 'disconnected') {logContent('not connected'); return;}
    
    let ssid = await ui.input_popup('Enter wifi ssid','', 'string');
    if(!ssid) return;

    ssid = ssid.toString();
    await bsh_ble_comm.commissioning.set_wifi_ssid(ssid);
    logContent('done');
}



async function set_pswd(): Promise<void>
{
    logContent('Setting wifi password');
    if(ble.main.get_connection_status() === 'disconnected') {logContent('not connected'); return;}

    let pswd = await ui.input_popup('Enter wifi password','', 'string');
    if(!pswd) return;

    pswd = pswd.toString();
    await bsh_ble_comm.commissioning.set_wifi_pswd(pswd);
    logContent('done');
}



async function connect_to_wifi(): Promise<void> 
{   
    logContent('Connecting to wifi');
    if(ble.main.get_connection_status() === 'disconnected') {logContent('not connected'); return;}

    await bsh_ble_comm.commissioning.connect_to_wifi();
}



async function ota_run(): Promise<void>
{
    logContent('Starting OTA');
    if(ble.main.get_connection_status() === 'disconnected') {logContent('not connected'); return;}
    await bsh_ble_comm.commissioning.start_ota_hardcoded();
}



async function ota_via_custom_link(): Promise<void>
{
    logContent('Starting OTA custom');
    if(ble.main.get_connection_status() === 'disconnected') {logContent('not connected'); return;}

    let link = await ui.input_popup('Enter url for firmware update','', 'string');
    if(!link) return;

    link = link.toString();
    await bsh_ble_comm.commissioning.start_ota_via_link(link);
}



async function set_balancer_link(): Promise<void>
{
    logContent('Setting balancer link');
    if(ble.main.get_connection_status() === 'disconnected') {logContent('not connected'); return;}

    let link = await ui.input_popup('Enter balancer server url','balancer.u.bork.ru:443', 'string');
    if(!link) return;

    link = link.toString();
    await bsh_ble_comm.commissioning.set_balancer_link(link);
    logContent('done');
}



async function get_broker_link(): Promise<void>
{
    logContent('Get broker from balancer');
    if(ble.main.get_connection_status() === 'disconnected') {logContent('not connected'); return;}

    await bsh_ble_comm.commissioning.run_balancer_request();
    logContent('done');
}



async function set_broker_id(): Promise<void>
{
    logContent('Setting broker id');
    if(ble.main.get_connection_status() === 'disconnected') {logContent('not connected'); return;}

    let id = await ui.input_popup('Enter id','', 'string');
    if(!id) return;

    id = id.toString();
    await bsh_ble_comm.commissioning.set_broker_id(id);
    logContent('done');
}



async function set_broker_pswd(): Promise<void>
{   
    logContent('Setting broker password');
    if(ble.main.get_connection_status() === 'disconnected') {logContent('not connected'); return;}

    let pswd = await ui.input_popup('Enter broker password','', 'string');
    if(!pswd) return;

    pswd = pswd.toString();
    await bsh_ble_comm.commissioning.set_broker_pswd(pswd);
    logContent('done');
}



async function connect_to_broker(): Promise<void>
{
    logContent('Connecting to broker');
    if(ble.main.get_connection_status() === 'disconnected') {logContent('not connected'); return;}

    await bsh_ble_comm.commissioning.connect_to_broker();
    logContent('done');
}



/****************************************
 *     read from device info chars
 ****************************************/
async function read_device_type(): Promise<void>
{
    if(ble.main.get_connection_status() === 'disconnected') {
        logContent('not connected'); 
        return;
    }

    const device_type = await bsh_ble_comm.device_info_getters.read_device_type();
    logContent(`device type: ${device_type.toString('hex')}`);
}



async function read_firmware_version(): Promise<void>
{
    if((ble as any).main.get_connection_status() === 'disconnected') {
        logContent('not connected');
        return;
    }

    const firmware_version = await bsh_ble_comm.device_info_getters.read_firmware_version();
    logContent(`firmware version: ${firmware_version}`);
}



/************************************
 *    other functions
 ***********************************/
function get_main_menu_items_list(): string[] {
    return main_menu.line_items.map(item => item.text);
}



function closing_submenu_callback(): void
{
    ui.ui_elements.main_menu_list.show();
    ui.ui_elements.main_menu_list.focus();
    ui.ui_elements.screen.render();
}



function data_from_device_cb(data: Buffer): void 
{
        logContent('    <<< incoming data: '. concat(data.toString('hex')), 'cyan');
        const parsed = replies_parser.parse_device_reply(data);

        if ('error' in parsed) {
            logContent(parsed.error, 'red');
            return;
        }
        logContent("      reply to: ".concat(parsed.reply_to," --> ", parsed.reply_msg));
}



function telemetry_cb(data: Buffer): void
{
    logContent('    <<< incoming telemetry: '. concat(data.toString('hex').substring(0, 10),'...'));

    const parsed_telemetry = telem_parser.parseTelemetryPacket(data);
    
    if(parsed_telemetry.error)
    {
        logContent(parsed_telemetry.error);
        return;
    }
    
    ui.updateStatusBar([parsed_telemetry.telemStrings[0], parsed_telemetry.telemStrings[1], null]);
}



function status_data_cb(data: Buffer): void 
{
    const parsed_data = bsh_ble_comm.main.connection_status_parser(data);
    if('error' in parsed_data)
    {
        logContent(parsed_data.error);
        return;
    }
    ui.updateStatusBar([null,null, parsed_data.as_string])
}



export { init, logContent }; 