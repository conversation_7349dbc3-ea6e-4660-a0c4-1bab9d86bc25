import * as fs from 'fs';
import * as path from 'path';

import ble from '../../BLE/ble';

interface RecipeStep {
    volume: number;          
    temperature: number;
    pattern: number;   // 0,1,2
    vibration: number; // 1,2,3
    pause: number; 
    grind_speed: number;
    flow_rate: number;
}

interface Recipe {
    steps: RecipeStep[];
    grind_size: number;
    ratio: number;
}

function get_main_recipes_settings_block(bypass_volume: number, bypass_temperature: number, grind_weight: number): Buffer {
    // Constant part of the buffer
    const constantPart = Buffer.from('580101A61F1800000001', 'hex');

    // Convert parameters to buffers
    const bypassVolumeBuffer = Buffer.alloc(4);
    bypassVolumeBuffer.writeUInt32LE(bypass_volume);

    const bypassTemperatureBuffer = Buffer.alloc(4);
    bypassTemperatureBuffer.writeUInt32LE(bypass_temperature);

    const grindWeightBuffer = Buffer.alloc(4);
    grindWeightBuffer.writeUInt32LE(grind_weight);

    // Combine all parts into a single buffer
    const combinedBuffer = Buffer.concat([constantPart, bypassVolumeBuffer, bypassTemperatureBuffer, grindWeightBuffer]);

    // Calculate CRC 
    const crcBuffer = ble.utils.calculate_crc16_modbus(combinedBuffer);
    
    // Append CRC to the combined buffer
    return Buffer.concat([combinedBuffer, crcBuffer]);
}

function get_cup_type_block(min_level: number, maxLevel: number): Buffer {
    // Constant part of the buffer
    const constantPart = Buffer.from('580101A81F1400000001', 'hex');

    // Convert float values to buffers
    const floatValue1Buffer = Buffer.alloc(4);
    floatValue1Buffer.writeFloatLE(min_level);

    const floatValue2Buffer = Buffer.alloc(4);
    floatValue2Buffer.writeFloatLE(maxLevel);

    // Combine all parts into a single buffer
    const combinedBuffer = Buffer.concat([constantPart, floatValue1Buffer, floatValue2Buffer]);

    // Calculate CRC
    const crcBuffer = ble.utils.calculate_crc16_modbus(combinedBuffer);
    
    // Append CRC to the combined buffer
    return Buffer.concat([combinedBuffer, crcBuffer]);
}



function recipe_to_string(recipe: Recipe): string {
    if (!checkRecipeInput(recipe)) {
        throw new Error("Invalid recipe object");
    }

    let recipeString = `Grind Size: ${recipe.grind_size}\n`;
    recipeString += `Ratio: ${recipe.ratio}\n`;
    recipeString += `Steps:\n`;

    recipe.steps.forEach((step, index) => {
        recipeString += `  Step ${index + 1}:\n`;
        recipeString += `    Volume: ${step.volume}\n`;
        recipeString += `    Temperature: ${step.temperature}\n`;
        recipeString += `    Pattern: ${step.pattern}\n`;
        recipeString += `    Vibration: ${step.vibration}\n`;
        recipeString += `    Pause: ${step.pause}\n`;
        recipeString += `    Grind Speed: ${step.grind_speed}\n`;
        recipeString += `    Flow Rate: ${step.flow_rate}\n`;
    });

    return recipeString;
}

function checkRecipeInput(recipe: Recipe): boolean {
    if (!Array.isArray(recipe.steps) || recipe.steps.length === 0) {
        return false; // If steps is not an array or is empty, return false
    }

    for (let i = 0; i < recipe.steps.length; i++) {
        const step = recipe.steps[i];
        if (i === 0) {
            // For the first step, check all fields
            if (isNaN(step.pause) || step.pause === 0) {
                return false; // If pause is NaN or equals 0, return false
            }
            if (Object.values(step).some(value => isNaN(value))) {
                return false;
            }
        } else {
            // For subsequent steps, allow NaN for grind_speed only
            if (isNaN(step.volume) || isNaN(step.temperature) || isNaN(step.pattern) || isNaN(step.vibration) || isNaN(step.pause) || isNaN(step.flow_rate)){
                return false;
            }
            if (isNaN(step.pause) || step.pause === 0) {
                return false; // If pause is NaN or equals 0, return false
            }
        }
    }

    // Check grind_size and ratio in the recipe object
    if (isNaN(recipe.grind_size) || isNaN(recipe.ratio)) {
        return false;
    }

    return true; 
}

function get_recipe_code(recipe: Recipe): Buffer {
    const buffers: Buffer[] = [];

    recipe.steps.forEach((step, index) => {
        const volume = step.volume;

        // Check if volume is greater than 127
        if (volume > 127) {
            // Create first step with volume 127
            const firstStepBuffer = Buffer.alloc(4);
            firstStepBuffer.writeUInt8(127, 0);
            firstStepBuffer.writeUInt8(step.temperature, 1);
            firstStepBuffer.writeUInt8(step.pattern, 2);
            firstStepBuffer.writeUInt8(step.vibration, 3);

            buffers.push(firstStepBuffer);

            // Create second step with the remaining volume
            const remainingVolume = volume - 127;
            const secondStepBuffer = Buffer.alloc(4);
            secondStepBuffer.writeUInt8(remainingVolume, 0);
            secondStepBuffer.writeUInt8(step.temperature, 1);
            secondStepBuffer.writeUInt8(step.pattern, 2);
            secondStepBuffer.writeUInt8(step.vibration, 3);
            buffers.push(secondStepBuffer);
            
            const thirdBuffer = Buffer.alloc(4);
            thirdBuffer.writeUInt8(0xff - step.pause + 1, 0);
            thirdBuffer.writeUInt8(0, 1);
            thirdBuffer.writeUInt8(index === 0 ? step.grind_speed : 0, 2);
            thirdBuffer.writeUInt8(step.flow_rate*10, 3);
            buffers.push(thirdBuffer);

        } else {
            // Create a single step buffer for volumes <= 127
            const stepBuffer = Buffer.alloc(8);
            stepBuffer.writeUInt8(volume, 0);
            stepBuffer.writeUInt8(step.temperature, 1);
            stepBuffer.writeUInt8(step.pattern, 2);
            stepBuffer.writeUInt8(step.vibration, 3);
            stepBuffer.writeUInt8(0xff - step.pause + 1, 4);
            stepBuffer.writeUInt8(0, 5);
            stepBuffer.writeUInt8(index === 0 ? step.grind_speed : 0, 6);
            stepBuffer.writeUInt8(step.flow_rate*10, 7);
            buffers.push(stepBuffer);
        }
    });

    const recipeBuffer = Buffer.concat(buffers);
    const grindSizeBuffer = Buffer.alloc(1);
    grindSizeBuffer.writeUInt8(recipe.grind_size);
    const ratioBuffer = Buffer.alloc(1);
    ratioBuffer.writeUInt8(recipe.ratio * 10);

    const finalBuffer = Buffer.concat([recipeBuffer, grindSizeBuffer, ratioBuffer]);
    const lengthByte = Buffer.alloc(1);
    lengthByte.writeUInt8(finalBuffer.length - 2); // Add single byte at the start with length - 2

    return Buffer.concat([lengthByte, finalBuffer]);
}

function get_recipe_block(recipe: Recipe): Buffer {
    if(!checkRecipeInput(recipe)) {
        throw new Error("wrong recipe object");
    }

    const recipeCodeBuffer = get_recipe_code(recipe);
    const totalSize = 10 + recipeCodeBuffer.length; 
    const buffer = Buffer.alloc(totalSize + 2); // Allocate buffer for total size + CRC

    // Write the header
    buffer.write('580101411F', 0, 'hex'); 

    // Write the total size in little-endian format
    buffer.writeUInt32LE(totalSize + 2, 5); // Write total size at offset 5
    buffer.writeUInt8(1, 9);
    // Write the recipe code buffer
    recipeCodeBuffer.copy(buffer, 10); // Copy recipe code to buffer starting at offset 10

    // Calculate and write CRC
    const crc = ble.utils.calculate_crc16_modbus(buffer.slice(0, totalSize)); // Calculate CRC for the relevant part
    crc.copy(buffer, totalSize); // Copy the CRC buffer to the end of the main buffer

    return buffer; 
}

function get_recipe_start_block(): Buffer {
    // Constant part of the buffer
    const constantPart = Buffer.from('580101AA0D0C000000010000', 'hex');
    return constantPart;
}

function getRecipiesFilesList(): string[] {
    // Read the list of JSON files in the recipes directory
    const recipesDir = path.join(__dirname, '../recipes');
    const files = fs.readdirSync(recipesDir);
    return files.filter(file => path.extname(file) === '.json');
}

export {
    get_main_recipes_settings_block,
    get_cup_type_block,
    recipe_to_string,
    checkRecipeInput,
    get_recipe_code,
    get_recipe_block,
    get_recipe_start_block,
    getRecipiesFilesList,
    Recipe,
    RecipeStep
}; 