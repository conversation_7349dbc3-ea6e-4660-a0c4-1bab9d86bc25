import { EventEmitter }         from 'node:events';
import ble                      from '../BLE/ble';
import * as commands            from './BSH_BLE_commands';
import { parseConnectionStatus }                    from './BSH_connection_status_parser';
import { BSH_CHAR_DEVICE_TYPE, BSH_CHAR_FIRM_VER }  from './bsh_config';
import { BSH_READ_CHAR }            from '../devices/O80x/O80x_config';

type LogCallback = (message: string) => void;

let external_logs: LogCallback | null = null;

// event emitter
class BshEventsEmitter extends EventEmitter {}
const bshEmitter = new BshEventsEmitter();
bshEmitter.setMaxListeners(Infinity);



/***********************************
 *      wifi commands
 ***********************************/
async function set_wifi_ssid(ssid: string): Promise<string | void> {
    try {
        await send_command(commands.set_wifi_ssid_start(ssid.length, 1, ssid.length));
        ble.utils.delay(100);
        await send_command(commands.set_wifi_ssid_data(1, ssid.length, Buffer.from(ssid)));
        ble.utils.delay(100);
        await send_command(commands.set_wifi_ssid_done());
    } catch (error) {
        if(external_logs) external_logs("error setting wifi ssid: ".concat(String(error)));
        return ("error setting wifi ssid: ".concat(String(error)));
    }
}



async function set_wifi_pswd(password: string): Promise<string | void> {
    try {
        await send_command(commands.set_wifi_pswd_start(password.length, 1, password.length));
        ble.utils.delay(100);
        await send_command(commands.set_wifi_pswd_data(1, password.length, Buffer.from(password)));
        ble.utils.delay(100);
        await send_command(commands.set_wifi_pswd_done());
    } catch (error) {
        if(external_logs) external_logs("error setting wifi password: ".concat(String(error)));
        return ("error setting wifi password: ".concat(String(error)));
    }
}



/**
 * @param {*} ssid         wifi ssid as string
 * @param {*} password     wifi password as string
 * @param {*} device       device object: {sendChar , receiveChar}
 * @returns 
 */
async function connect_to_wifi(): Promise<void> {
    await send_command(commands.connect_to_wifi());
}



/***********************************
 *      ota commands
 ***********************************/
async function start_ota_hardcoded(): Promise<void> {
    await send_command(commands.start_ota_hardcoded());
}

async function start_ota_via_link(link: string): Promise<void> {
    await send_command(commands.start_ota_via_link(link));
}



/***********************************
 *      balancer commands
 ***********************************/
async function set_balancer_link(link: string): Promise<void> {
    await send_command(commands.set_balancer_link_start(link.length, 1, link.length));
    ble.utils.delay(200);
    await send_command(commands.set_balancer_link_data(1, link.length, Buffer.from(link)));
    ble.utils.delay(200);
    await send_command(commands.set_balancer_link_done());
}



async function run_balancer_request(): Promise<void> {
    await send_command(commands.connect_to_balancer());
}



/***********************************
 *      broker commands
 ***********************************/
async function set_broker_id(id: string): Promise<void> {
    await send_command(commands.set_user_id_start(id.length, 1, id.length));
    ble.utils.delay(200);
    await send_command(commands.set_user_id_data(1, id.length, Buffer.from(id)));
    ble.utils.delay(200);
    await send_command(commands.set_user_id_done());
}



async function set_broker_pswd(pswd: string): Promise<void> {
    await send_command(commands.set_mqtt_pswd_start(pswd.length, 1, pswd.length));
    ble.utils.delay(200);
    await send_command(commands.set_mqtt_pswd_data(1, pswd.length, Buffer.from(pswd)));
    ble.utils.delay(200);
    await send_command(commands.set_mqtt_pswd_done());
}



async function connect_to_broker(): Promise<void> {
    await send_command(commands.connect_to_broker());
}



/***********************************
 * Device info getters
 * Функции получения информации об устройстве - ID, challenge, challenge sign  и т.п.
 ***********************************/
async function get_model(): Promise<void> {
    await send_command(commands.get_model());
}

async function get_device_id(): Promise<void> {
    await send_command(commands.get_id());
}

async function get_firmware_version(): Promise<void> {
    await send_command(commands.get_firmware_version());
    // let version_string = reply[3].toString() + '.' + reply[4].toString() + '.' + reply[5].toString()
}

async function read_device_type(): Promise<any> {
    return await ble.main.read_data_from_char(BSH_CHAR_DEVICE_TYPE);
}

async function read_firmware_version(): Promise<any> {
    return await ble.main.read_data_from_char(BSH_CHAR_FIRM_VER);
}



/**
 *    main block of commands
 */
let read_char_is_set = false;
async function send_command(command: Buffer): Promise<void> {
    if(!read_char_is_set) 
    {
        await ble.low_level.set_receive_char(BSH_READ_CHAR);
        read_char_is_set = true;
    }

    try {
        await ble.low_level.sendToDevice_AndWaitForReply(command);
    } catch (error) {
        if(external_logs) external_logs("error sending command: ".concat(String(error)));
    }
}



// ============== utils ================
function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function enable_logs(logs_cb: LogCallback): void {
    external_logs = logs_cb;
}





// ============== exports ================
export const main = {
    connection_status_parser: parseConnectionStatus,    // re-export
    send_command,             // просто отправляет команду на указанную характеристику 
};


export const commissioning = {
    set_wifi_ssid,           // (ssid, device) device object: {sendChar , receiveChar}
    set_wifi_pswd,           // (password, device) device object: {sendChar , receiveChar}
    connect_to_wifi,    // (device) device object: {sendChar , receiveChar}
    start_ota_hardcoded,
    start_ota_via_link,
    set_balancer_link,
    run_balancer_request,
    set_broker_id,
    set_broker_pswd,
    connect_to_broker,
};

export const device_info_getters = {
    get_model,
    get_firmware_version,
    get_device_id,
    read_device_type,
    read_firmware_version,
    // get_challenge,
    // get_challenge_sign,
};

export const utils = {
    enable_logs,          // accepts logs_cb function as a parameter
    delay
};