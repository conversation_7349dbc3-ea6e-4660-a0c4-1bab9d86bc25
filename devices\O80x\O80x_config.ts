export const    DEVICE_NAME          :string = 'O80x';
export const    DEVICE_MAC           :string = 'dc:54:75:c7:e5:d6';                      
export const    DEVICE_UUID          :string = '51b7513274e7ed478cc6aa22c884056b';   // TODO - insert real id
    
export const    BSH_CONTROL_SERVICE  :string = "00F3";    
export const    BSH_CHAR_DEVICE_TYPE :string = 'F301';
export const    BSH_CHAR_FIRM_VER    :string = 'F302';
export const    BSH_WRITE_CHAR       :string = 'F303';
export const    BSH_READ_CHAR        :string = 'F304';
export const    BSH_CHAR_STATUS      :string = 'F305';
export const    BSH_CHAR_TELEM       :string = 'F306';

export const    BLE_SCAN_TIME        :number = 10;

