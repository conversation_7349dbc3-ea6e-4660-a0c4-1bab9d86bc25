import ble from '../BLE/ble';
import { Command, CommandParameter} from './CommandTypes/command_types';
// import { Peripheral } from '@abandonware/noble';
import { Peripheral } from '../BLE/ble';
import * as ui from '../UI/ui_common_logic';



/**
 * Send raw data to the connected BLE device
 */
async function send_raw_data(): Promise<void> {
    const raw_data = await ui.input_popup(' enter raw data in hex (like 80594493FB)', '', 'string') as string;
    if (!raw_data) return;
    
    if (raw_data.length % 2 !== 0) {
        ui.logContent('wrong hex string length');
        return;
    }
    try {
        ble.main.send_data(Buffer.from(raw_data, "hex"));
    } catch (error) {
        ui.logContent('wrong hex string'.concat(error.toString()));
        return;
    }
}



/**
 * Ask the user for parameter values for a command
 * Deliberately doesn't check for value ranges - to allow out-of-boundary values testing
 * @param command - The command object with parameter definitions
 * @returns Array of user input values or null if error
 */
async function ask_user_for_parameters(command: Command): Promise<number[] | null> {
    if (command.params_qty === 0) { return []; }

    const user_inputs_array: number[] = [];

    for (let i = 1; i <= command.params_qty; i++) {
        const param_name = `p${i}`;
        const param = command[param_name];
        if (param) {
            if(param.type === 'int' || param.type === 'float') {   
                try {
                    const input = await ui.input_popup(
                        ' enter '.concat(param.name, ' in range: ', param.min?.toString() || '', ' - ', param.max?.toString() || ''), 
                        param.default_value === undefined ? "" : param.default_value.toString(), 
                        param.type
                    ) as number | null;
                    
                    user_inputs_array[i - 1] = input as number;
                    if(user_inputs_array[i - 1] != null && user_inputs_array[i - 1] != undefined) {
                        param.default_value = user_inputs_array[i - 1];
                    }
                } catch (error) {
                    ui.logContent(error as string);
                    return null;
                }
            } else if (param.type === 'enum' && param.options) {
                const user_picked_option = await ui.show_select_popup(param.options, ' pick '.concat(param.name, ' option'));
                if (user_picked_option) {
                    user_inputs_array[i - 1] = user_picked_option.index + (param.min || 0);
                }
            }
        }
    }
    return user_inputs_array;
}

/********************************
 *   BLE connection functions
 *******************************/
/**
 * Connect to a BLE device by MAC address
 */
async function connect_by_mac(
    mac: string, 
    uuid: string, 
    TX_char: string, 
    RX_char: string, 
    data_callback: (data: Buffer, isNotification: boolean) => void
): Promise<void> {
    let deviceToConnect: Peripheral | null = null;

    // Try by MAC if not running on iOS,  else try by uuid
    if (process.platform === 'darwin') {  // MacOS
        deviceToConnect = await ble.main.find_device_by_uuid(uuid);
    } else {
        deviceToConnect = await ble.main.find_device_by_mac(mac);
    }
    if (deviceToConnect) {
        const services = await ble.main.connect(deviceToConnect);
        if (services) {
            ui.logContent('connected', 'green');
            await ble.main.set_TX_char(TX_char);
            await ble.main.subscribe_to(RX_char, data_callback);
            ui.logContent('done','green');

        } else {
            ui.logContent('cant connect to device','yellow');
        }
    } else {
        ui.logContent('Устройство не найдено','yellow');
    }
}



/**
 * Connect to the nearest BLE device with the specified name or service UUID
 */
async function connect_to_nearest(
    device_name: string | null, 
    service_uuid: string | null, 
    TX_char: string, 
    RX_char: string, 
    data_callback: (data: Buffer, isNotification: boolean) => void
): Promise<void> {

    let devices_list: Peripheral[];

  
    if(device_name != null) {
        devices_list = await ble.main.find_all_by_name(device_name);
    } else if (service_uuid != null) {
        devices_list = await ble.main.find_all_by_service(service_uuid);
    } else {
        ui.logContent("internal err: empty params",'red');
        return;
    }
    if(devices_list.length === 0) {
        ui.logContent('Устройство не найдено','yellow');
        return;
    }

    const closest_device = ble.utils.get_periph_with_better_signal_from_array(devices_list);
    if (closest_device === undefined || closest_device === null) {
        ui.logContent ('Connection err','red');
        return;
    }

    const services = await ble.main.connect(closest_device);
    if (services) {
        ui.logContent('connected. setting TX char','green');
        
        await ble.main.set_TX_char(TX_char);
        ui.logContent("done. subscribing to RX char",'green');

        await ble.main.subscribe_to(RX_char, data_callback);
        ui.logContent('done','green');
    } else {
        ui.logContent('CANT CONNECT TO DEVICE','red');
    }
}



/**
 * Pick a device from a list and connect to it
 */
async function pick_from_list_and_connect(
    device_name: string | null, 
    service_uuid: string | null, 
    TX_char: string, 
    RX_char: string, 
    data_callback: (data: Buffer, isNotification: boolean) => void
): Promise<void> {
    let devices_list: Peripheral[];
    
    if(device_name != null) {
        devices_list = await ble.main.find_all_by_name(device_name);
    } else if (service_uuid != null) {
        devices_list = await ble.main.find_all_by_service(service_uuid);
    } else {
        return;
    }

    if(devices_list.length === 0) {
        ui.logContent('Устройство не найдено','yellow');
        return;
    }

    const devices_list_for_popup: string[] = [];
    for (let i = 0; i < devices_list.length; i++) {
        const device = devices_list[i];
        const device_name = device.advertisement.localName || '';
        const device_mac = device.address;
        const device_id = device.id;
        const device_string = device_name + ' | ' + device_mac + ' | ' + device_id;
        devices_list_for_popup.push(device_string);
    }

    const device_to_connect_to = await ui.show_select_popup(devices_list_for_popup, ' Pick device ');
    if(device_to_connect_to === null) return;
    
    const deviceToConnect = devices_list[device_to_connect_to.index];

    const services = await ble.main.connect(deviceToConnect);
    if (services) {
        ui.logContent('connected. setting TX char '.concat(TX_char),'green');

        await ble.main.set_TX_char(TX_char);
        ui.logContent("done. subscribing to RX char ".concat(RX_char),'green');
        await ble.main.subscribe_to(RX_char, data_callback);
        ui.logContent('done','green');
    } else {
        ui.logContent('CANT CONNECT TO DEVICE','red');
    }
}



export {
    send_raw_data,
    ask_user_for_parameters,
    connect_by_mac,
    connect_to_nearest,
    pick_from_list_and_connect
}

export { Command, CommandParameter }