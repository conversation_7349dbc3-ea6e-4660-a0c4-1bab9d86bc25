/* Reply/status change packet structure:
 * 1:       protocol version 0x01
 * 2:       qty of segments
 * 3,4,5:   actor
 * 6:       index: if multiple actors of same type
 * 7:       capability 
 * 8:       action type - always 0x80 here
 * 9:       DATA_TYPE
 * 10,11,12,13:      data
 */

interface Segment {
    actor: string;
    index: number;
    capability: number;
    actionType: number;
    dataType: number;
    data: number;
}

interface TelemetryResult {
    protocolVersion?: number;
    segmentQty?: number;
    segments?: Segment[];
    telemStrings?: string[];  // telemetry as string - to print to console etc
    error?: string;
}

function parseTelemetryPacket(buffer: Buffer): TelemetryResult 
{
    if (buffer.length < 2) 
    {
        return { error: `too small packet: ${buffer.length}` };
    }

    const protocolVersion = buffer.readUInt8(0);
    const segmentQty = buffer.readUInt8(1);
    const expectedLength = 2 + segmentQty * 9;
    if (buffer.length < expectedLength)
    {
        return { error: `too small packet for ${segmentQty} segments: ${buffer.length}, expected ${expectedLength}` };
    }
    
    const segments: Segment[] = [];
    for (let i = 0; i < segmentQty; i++)
    {
        const offset = 2 + i * 11;
        const actorCode = buffer.readUIntBE(offset, 3);
        const actor = String.fromCharCode(
            (actorCode >> 16) & 0xFF,
            (actorCode >> 8) & 0xFF,
            actorCode & 0xFF);
        const index = buffer.readUInt8(offset + 3);
        const capability = buffer.readUInt8(offset + 4);
        const actionType = buffer.readUInt8(offset + 5);
        const dataType = buffer.readUInt8(offset + 6);
        const data = buffer.readUInt32LE(offset + 7);
        segments.push({ actor, index, capability, actionType, dataType, data });
    }

    const telemStrings: string[] = [];
    let current_string = "";
    for (const segment of segments) {
        current_string = current_string.concat(`${segment.actor}: ${segment.data}  `);
        if (current_string.length > 110) {
            telemStrings.push(current_string);
            current_string = "";
        }
    }
    telemStrings.push(current_string);

    return {
        protocolVersion,
        segmentQty,        
        segments,
        telemStrings
    };
}



export {
    parseTelemetryPacket
} 