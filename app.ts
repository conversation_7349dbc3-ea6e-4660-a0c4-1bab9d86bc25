// Requires

import ble from './BLE/ble';

import * as device_select from './UI/device_select_screen';
// import * as ui from './UI/ui_common_logic';
// ui.ui_elements.screen.leave();   // если раскоментить - не рендерится экран - чтобы посмотреть вывод в консоль за ним
import * as c781 from './devices/C781/c781_main_menu';
import * as d707 from './devices/D707/ui/D707_main_menu';
import * as o80x from './devices/O80x/ui/O80x_main_menu';



type LogContentFunction = (text: string, color?: string) => void;

let logContent : LogContentFunction;



async function main_logic() 
{
    const device = await device_select.show_select_popup(['C781 кофемашина', 'D707 массажер', 'O80x обогреватель'], 'Выберите устройство');
    device_select.close_screen();

    if (device === null) 
    {
        process.exit(0);
    }
    
    switch (device.content) {
        case null:
            return process.exit(0);

        case 'C781 кофемашина':
            c781.init();
            logContent = c781.logContent;
            break;
        case 'D707 массажер':
            d707.init();
            logContent = d707.logContent;
            break;
        case 'O80x обогреватель':
            o80x.init();
            logContent = o80x.logContent;
            break;
    }

    if(logContent !== null)
    {
        logContent (" *********************************************************************** ");
        logContent ("   Подключится к устройству можно следующими способами: ");
        logContent ("   К ближайшему устройству ");
        logContent ("   Просканировать эфир и выбрать из списка. ");
        logContent ("   Просканировать эфир и вписать мак адрес устройства в конфиг:");
        logContent ("   в файле ***_config.ts : DEVICE_MAC = ... ");
        logContent ("   Если у вас MacOS - надо прописать UUID в DEVICE_UUID =  ");
        logContent ("   !!! для D707 поиск надо запускать дважды !!!");
        logContent (" *********************************************************************** ");
    }
    
    await ble.main.init_ble(logs_from_ble);
}




/*****************************
 *          functions
 *****************************/
function logs_from_ble(text : string, color? : string) 
{
    logContent("    < BLE >    ".concat(text), color);
}



main_logic();
