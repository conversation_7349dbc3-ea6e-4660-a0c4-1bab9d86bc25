interface CommandId {
    code: number;
    text: string;
}

interface CommandIds {
    [key: string]: CommandId;
}


// wifi ssid
export function set_wifi_ssid_start(ssid_size: number, pieces_qty: number, piece_size: number) : Buffer {
    return Buffer.from   ([0x10, ssid_size % 0x100 ,Math.floor(ssid_size / 0x100) ,pieces_qty ,piece_size]); 
};
export function set_wifi_ssid_data(piece_N: number, piece_size: number, data: Buffer) : Buffer {
    return Buffer.concat ([Buffer.from([0x11 ,piece_N ,piece_size]), data]);
};
export function set_wifi_ssid_done() : Buffer { 
    return Buffer.from   ([0x12]) 
};
export function set_wifi_ssid_cancel() : Buffer { 
    return Buffer.from   ([0x13]) 
};



// wifi pswd
export function set_wifi_pswd_start(pswd_size: number, pieces_qty: number, piece_size: number) : Buffer {
    return Buffer.from   ([0x20,pswd_size % 0x100 ,pswd_size / 0x100 ,pieces_qty ,piece_size]);
};
export function set_wifi_pswd_data(piece_N: number, piece_size: number, data: Buffer) : Buffer {
    return Buffer.concat ([Buffer.from([0x21 ,piece_N ,piece_size]), data]);
};
export function set_wifi_pswd_done() : Buffer {  
    return Buffer.from   ([0x22]) 
};
export function set_wifi_pswd_cancel() : Buffer { 
    return Buffer.from   ([0x23]) 
};



// balancer link
export function set_balancer_link_start(link_size: number, pieces_qty: number, piece_size: number) : Buffer{
    return Buffer.from   ([0x40,link_size % 0x100 ,link_size / 0x100 ,pieces_qty ,piece_size]);
};
export function set_balancer_link_data(piece_N: number, piece_size: number, data: Buffer) : Buffer{
    return Buffer.concat ([Buffer.from([0x41 ,piece_N ,piece_size]), data]);
}
export function set_balancer_link_done()    : Buffer{  
    return Buffer.from   ([0x42]) 
};
export function set_balancer_link_cancel()  : Buffer{  
    return Buffer.from   ([0x44]) 
};
    


// mqtt password
export function set_mqtt_pswd_start(pswd_size: number, pieces_qty: number, piece_size: number) : Buffer{
    return Buffer.from   ([0x50,pswd_size % 0x100 ,pswd_size / 0x100 ,pieces_qty ,piece_size]);
}
export function set_mqtt_pswd_data(piece_N: number, piece_size: number, data: Buffer) : Buffer{
    return Buffer.concat ([Buffer.from([0x51 ,piece_N ,piece_size]), data]);
}
export function set_mqtt_pswd_done() : Buffer{
    return Buffer.from   ([0x52]);
}
export function set_mqtt_pswd_cancel() : Buffer{
    return Buffer.from   ([0x53]);
}
    


// user id
export function set_user_id_start(id_size: number, pieces_qty: number, piece_size: number) : Buffer{
    return Buffer.from   ([0x60,id_size % 0x100 ,id_size / 0x100 ,pieces_qty ,piece_size]);
}
export function set_user_id_data(piece_N: number, piece_size: number, data: Buffer) : Buffer{
    return Buffer.concat ([Buffer.from([0x61 ,piece_N ,piece_size]), data]);
}
export function set_user_id_done() : Buffer{
    return Buffer.from   ([0x62]);
}
export function set_user_id_cancel() : Buffer{
    return Buffer.from   ([0x63])
}
    


// Команды подключения
export function connect()               : Buffer { return Buffer.from([0x30]) };
export function connect_to_wifi()       : Buffer { return Buffer.from([0x31]) };
export function connect_to_balancer()   : Buffer { return Buffer.from([0x32]) };
export function connect_to_broker()     : Buffer { return Buffer.from([0x33]) };



// Команды получения параметров устройства
export function get_model()                 : Buffer { return Buffer.from([0x70]) };
export function get_id()                    : Buffer { return Buffer.from([0x80]) };
export function get_firmware_version()      : Buffer { return Buffer.from([0x90]) };
export function get_challenge()             : Buffer { return Buffer.from([0xA0]) };
export function get_challenge_sign_size()   : Buffer { return Buffer.from([0xB0]) };
export function get_challenge_piece_size()  : Buffer { return Buffer.from([0xB1]) };
export function get_piece_of_challenge_sign(piece_number: number) : Buffer {
    return Buffer.from([0xB2, piece_number]); 
};
    
// Команды ОТА
export function start_ota_hardcoded() : Buffer {
    return Buffer.from([0xFA]);
}
export function start_ota_via_link(link: string) : Buffer {
    return Buffer.concat([Buffer.from([0xFB, link.length % 255, link.length / 255]), Buffer.from(link, 'utf8')]);
}
    
export function reboot() : Buffer { return Buffer.from([0xF1,0xF2,0xF3]) };



export function find_command_by_code(code : number) : CommandId | null {
    for (const key in commands_IDs) {
        if (typeof commands_IDs[key] === 'object' && commands_IDs[key].code === code) {
            return commands_IDs[key];
        }
    }
    return null;
}

    
    // ID команд
const commands_IDs: CommandIds = {

    ID_set_wifi_ssid_start:           { code: 0x10, text: 'set wifi ssid start' }, 
    ID_set_wifi_ssid_data:            { code: 0x11, text: 'set wifi ssid data' },
    ID_set_wifi_ssid_done:            { code: 0x12, text: 'set wifi ssid done' },
    ID_set_wifi_ssid_cancel:          { code: 0x13, text: 'set wifi ssid cancel' },

    ID_set_wifi_pswd_start:           { code: 0x20, text: 'set wifi pswd start' },
    ID_set_wifi_pswd_data:            { code: 0x21, text: 'set wifi pswd data' },
    ID_set_wifi_pswd_done:            { code: 0x22, text: 'set wifi pswd done' },
    ID_set_wifi_pswd_cancel:          { code: 0x23, text: 'set wifi pswd cancel' },

    ID_connect_bsh:                   { code: 0x30, text: 'connect bsh' },
    ID_connect_to_wifi:               { code: 0x31, text: 'connect to wifi' },
    ID_connect_to_balancer:           { code: 0x32, text: 'connect to balancer' },
    ID_connect_to_broker:             { code: 0x33, text: 'connect to broker' },

    ID_set_balancer_link_start:       { code: 0x40, text: 'set balancer link start' },
    ID_set_balancer_link_data:        { code: 0x41, text: 'set balancer link data' },
    ID_set_balancer_link_done:        { code: 0x42, text: 'set balancer link done' },
    ID_set_balancer_link_cancel:      { code: 0x43, text: 'set balancer link cancel' },

    ID_set_mqtt_pswd_start:           { code: 0x50, text: 'set mqtt pswd start' },
    ID_set_mqtt_pswd_data:            { code: 0x51, text: 'set mqtt pswd data' },
    ID_set_mqtt_pswd_done:            { code: 0x52, text: 'set mqtt pswd done' },
    ID_set_mqtt_pswd_cancel:          { code: 0x53, text: 'set mqtt pswd cancel' },

    ID_set_user_id_start:             { code: 0x60, text: 'set user id start' },
    ID_set_user_id_data:              { code: 0x61, text: 'set user id data' },
    ID_set_user_id_done:              { code: 0x62, text: 'set user id done' },
    ID_set_user_id_cancel:            { code: 0x63, text: 'set user id cancel' },

    ID_send_model:                    { code: 0x70, text: 'send model' },
    ID_send_id:                       { code: 0x80, text: 'send id' },
    ID_send_firmware_version:         { code: 0x90, text: 'send firmware version' },
    ID_send_challenge:                { code: 0xA0, text: 'send challenge' },
    ID_send_challenge_sign_size:      { code: 0xB0, text: 'send challenge sign size' },
    ID_send_challenge_piece_size:     { code: 0xB1, text: 'send challenge piece size' },
    ID_send_piece_of_challenge_sign:  { code: 0xB2, text: 'send piece of challenge sign' },

    ID_start_ota_hardcoded:           { code: 0xFA, text: 'start ota hardcoded' },
    ID_start_ota_via_link:            { code: 0xFB, text: 'start ota via link' },

    reply_to_mqtt_via_ble:            { code: 0xf6, text: 'mqtt via ble cmd' },

};

export {commands_IDs}; 