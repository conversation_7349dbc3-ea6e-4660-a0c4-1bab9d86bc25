/**
*             device reply:
*             [ 0xAA ]                        device reply byte
*             [ 0x10 ]                        repeating command 
*             [ 0x00 ]                        spacer
*             [ 0x00 = ok   or 0x01 = error]  status  
*/
import * as commands from './BSH_BLE_commands';



interface ReplyItem {
    code: number;
    text: string;
}

interface ParsedReply {
    reply_to: string;
    reply_msg: string;
}

interface ParseError {
    error: string;
}

interface Replies {
    [key: string]: ReplyItem;
}    



function parse_device_reply(buffer: Buffer): ParsedReply | ParseError {
    if (buffer.length < 2) 
    {   
        return ({error: 'cant parse: buffer too small'});
    }

    if (buffer[0] !== 0xaa && buffer[0] !== 0xf6) 
    {
        return ({error: 'packet error: wrong header'})
    }

    let reply : string;
    switch (buffer[1]) {
        case 0x70:
            reply = buffer.subarray(3).toString();
            break;
    
        case 0x80:
            reply = buffer.subarray(3).toString('hex');
            break;
    
        case 0x90:
            reply = `${buffer[3]}.${buffer[4]}.${buffer[5]}}` 
            break;

        default:
            if (buffer[0] === 0xf6)
            {
                reply = (buffer[1] === 0 ? 'ok' : 'error');
                break;
            }

            reply = find_reply_by_code(buffer[3]) ? find_reply_by_code(buffer[3]).text : `unknown message ${buffer[3].toString(16)}`;
            break;
    }
    const reply_code = buffer[0] === 0xf6 ? buffer[0] : buffer[1];

    return {
            reply_to: commands.find_command_by_code(reply_code) ? commands.find_command_by_code(reply_code)!.text : `unknown code: ${reply_code.toString(16)}`,
            reply_msg: reply
        }
}



function find_reply_by_code(code: number): ReplyItem | null {
    for (const key in replies) {
        if (typeof replies[key] === 'object' && replies[key].code === code) {
            return replies[key];
        }
    }
    return null;
}



const replies: Replies = {
    header:                 {code: 0xaa, text: "header"},

    // replies to setters                       !! put "err" into descriptions as appropriate so messages can be color-coded properly when printed into console
    ok:                     { code: 0x00, text: 'ok' },
    packer_err:             { code: 0x01, text: 'packet error' },
    already_started:        { code: 0x02, text: 'setting process already started' },
    not_started:            { code: 0x03, text: 'setting process not started' },

    wifi_ssid_ok:           { code: 0x11, text: 'wifi ssid set' },
    wifi_pswd_ok:           { code: 0x21, text: 'wifi pswd set' },
    link_ok:                { code: 0x41, text: 'balancer link set' },
    mqtt_pswd_ok:           { code: 0x51, text: 'mqtt passwrord set' },
    mqtt_user_id_ok:        { code: 0x61, text: 'mqtt user id set' },

    // replies to device info request
    info_model:             { code: 0x70, text: 'model' },
    info_device_id:         { code: 0x80, text: 'device id' },
    info_firmware_version:  { code: 0x90, text: 'firmware version'},

    // replies to connect to wifi
    wifi_ssid_not_set:      { code: 0xc8, text: 'wifi err: ssid is not set' },
    wifi_pswd_not_set:      { code: 0xc9, text: 'wifi err: password is not set' },
    wifi_connected:         { code: 0xca, text: 'wifi connected' },
    wifi_no_network:        { code: 0xcb, text: 'wifi err: no network' },
    wifi_wrong_pswd:        { code: 0xcc, text: 'wifi err: wrong password' },
    wifi_disconnected:      { code: 0xcd, text: 'wifi disconnected' },
    wifi_connecting:        { code: 0xce, text: 'wifi connecting' },
    wifi_cant_get_ip:       { code: 0xcf, text: 'wifi err: cant get ip' },

    // replies to balancer request
    blcr_got_link:          { code: 0xf0, text: 'balancer: got link' },
    blcr_cant_reach:        { code: 0xf1, text: 'balancer err: cant reach' },
    blcr_wrong_reply:       { code: 0xf2, text: 'balancer err: wrong reply' },
    blcr_no_link_set:       { code: 0xf3, text: 'balancer err: no link set' },
    blcr_connecting:        { code: 0xf4, text: 'balancer: connecting' },
    blcr_no_wifi:           { code: 0xf5, text: 'balancer err: no wifi' },

    // replies to broker connect
    brk_no_user_id_set:     { code: 0x65, text: 'broker err: no user id set' },
    brk_no_user_pswd_set:   { code: 0x66, text: 'broker err: no user pswd set' },
    brk_connected:          { code: 0x67, text: 'broker: connected' },
    brk_subscribed:         { code: 0x68, text: 'broker: subscribed' },
    brk_msg_published:      { code: 0x69, text: 'broker: msg_published' },
    brk_cant_reach:         { code: 0x6a, text: 'broker err: cant reach' },
    brk_connection_refused: { code: 0x6b, text: 'broker err: connection_refused' },
    brk_no_link_set:        { code: 0x6c, text: 'broker err: no_link_set' },
    brk_disconnected:       { code: 0x6d, text: 'broker: disconnected' },
    brk_connecting:         { code: 0x6e, text: 'broker: connecting' },
    brk_no_wifi:            { code: 0x6f, text: 'broker err: no_wifi' },
};



export {parse_device_reply,
          replies}; 