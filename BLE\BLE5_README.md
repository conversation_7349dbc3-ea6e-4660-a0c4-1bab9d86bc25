# BLE 5.0 Enhanced Features

This document describes the enhanced BLE 5.0 features implemented in the BLE module.

## Overview

The BLE module now supports advanced Bluetooth 5.0 features including:

- **Extended Advertising** - Larger advertisement payloads (up to 1650 bytes)
- **PHY Selection** - Support for 1M, 2M, and Coded PHY
- **Enhanced Scanning** - BLE 5.0 specific scanning parameters
- **Connection Parameter Management** - Advanced connection monitoring
- **Device Capability Detection** - Automatic BLE 5.0 feature detection

## New Types

### PHYType
```typescript
type PHYType = '1M' | '2M' | 'Coded';
```

### BLE5Config
```typescript
interface BLE5Config {
    preferredPHY?: PHYType;
    extendedAdvertising?: boolean;
    periodicAdvertising?: boolean;
    codedPHYSupport?: boolean;
    advertisingInterval?: number;
    maxAdvertisingDataLength?: number;
}
```

### ExtendedAdvertisement
```typescript
interface ExtendedAdvertisement extends noble.Advertisement {
    extendedData?: Buffer;
    periodicData?: Buffer;
    primaryPHY?: PHYType;
    secondaryPHY?: PHYType;
    advertisingInterval?: number;
    txPower?: number;
    addressType?: AddressType;
    isExtended?: boolean;
    isPeriodic?: boolean;
    isConnectable?: boolean;
    isScannable?: boolean;
}
```

## New Functions

### Initialization with BLE 5.0 Config

```typescript
// Initialize with BLE 5.0 configuration
await ble.main.init_ble(loggingFunction, {
    preferredPHY: '2M',
    extendedAdvertising: true,
    codedPHYSupport: true,
    advertisingInterval: 100
});
```

### Enhanced Scanning

```typescript
// Start BLE 5.0 enhanced scanning
await ble.ble5.start_ble5_scanning([], {
    allowDuplicates: false,
    scanPHY: '2M',
    extendedScan: true,
    activeScan: true
});
```

### PHY Management

```typescript
// Update connection PHY
const success = await ble.ble5.update_connection_phy(peripheral, '2M', '2M');

// Check device BLE 5.0 support
const support = ble.ble5.check_ble5_support(peripheral);
console.log('Extended Advertising:', support.extendedAdvertising);
console.log('Supported PHYs:', support.phySupport);
console.log('Coded PHY:', support.codedPHY);
```

### Configuration Management

```typescript
// Get current configuration
const config = ble.ble5.get_ble5_config();

// Update configuration
ble.ble5.update_ble5_config({
    preferredPHY: 'Coded',
    advertisingInterval: 200
});
```

### Connection Parameters

```typescript
// Get connection parameters
const params = ble.ble5.get_connection_parameters(peripheral);
console.log('Connection Interval:', params.interval);
console.log('Current PHY:', params.phy);
console.log('MTU Size:', params.mtu);
```

## Usage Examples

### Basic BLE 5.0 Setup

```typescript
import ble from './ble';

async function setupBLE5() {
    // Initialize with 2M PHY preference
    await ble.main.init_ble(console.log, {
        preferredPHY: '2M',
        extendedAdvertising: true
    });

    // Start enhanced scanning
    await ble.ble5.start_ble5_scanning();
}
```

### Long Range Configuration

```typescript
// Configure for long-range operation using Coded PHY
ble.ble5.update_ble5_config({
    preferredPHY: 'Coded',
    codedPHYSupport: true,
    advertisingInterval: 200
});
```

### High-Speed Configuration

```typescript
// Configure for high-speed operation using 2M PHY
ble.ble5.update_ble5_config({
    preferredPHY: '2M',
    advertisingInterval: 20,
    maxAdvertisingDataLength: 1650
});
```

## Hardware Requirements

### Minimum Requirements
- Bluetooth 5.0+ compatible adapter
- Compatible drivers (Linux: BlueZ 5.50+, Windows: WinUSB driver)
- Node.js with @abandonware/noble 1.9.2-26+

### Recommended Hardware
- Intel AX200/AX201 (Windows/Linux)
- Broadcom BCM20702A0 (macOS)
- Nordic nRF52840 USB dongles
- Any Bluetooth 5.0+ certified adapter

## Platform Support

| Feature | Linux | Windows | macOS |
|---------|-------|---------|-------|
| Extended Advertising | ✅ | ✅ | ⚠️ |
| 2M PHY | ✅ | ✅ | ⚠️ |
| Coded PHY | ✅ | ✅ | ❌ |
| PHY Updates | ✅ | ✅ | ❌ |

⚠️ = Limited support, ❌ = Not supported

## Troubleshooting

### PHY Update Not Working
- Ensure both devices support the target PHY
- Check that the connection is established
- Verify platform support for PHY updates

### Extended Advertising Not Detected
- Confirm the advertising device uses BLE 5.0 extended advertising
- Check that `extendedAdvertising` is enabled in configuration
- Verify adapter supports extended advertising

### Performance Issues
- Use 2M PHY for higher throughput
- Use Coded PHY for longer range
- Adjust advertising intervals based on use case

## Migration from Legacy Code

### Before (Legacy)
```typescript
await ble.main.init_ble(loggingFunction);
await ble.main.scan_all(10);
```

### After (BLE 5.0 Enhanced)
```typescript
await ble.main.init_ble(loggingFunction, { preferredPHY: '2M' });
await ble.ble5.start_ble5_scanning([], { scanPHY: '2M' });
```

## See Also

- [BLE 5.0 Example](./ble5_example.ts) - Complete working examples
- [Noble Documentation](https://github.com/abandonware/noble) - Base library documentation
- [Bluetooth 5.0 Specification](https://www.bluetooth.com/specifications/bluetooth-core-specification/) - Official specification
