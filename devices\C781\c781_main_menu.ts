import * as fs from 'fs';
import * as path from 'path';

import ble from '../../BLE/ble';
import * as ui from '../../UI/ui_common_logic';

import * as commands_menu from './c781_commands_submenu';
import * as recipes from './recipes';
import * as commands from './c781_commands';
// { make_command, commands_list, get_command_name_by_code } from './c781_commands';
import * as telemetry_parser from './telemetry_parser';
import * as utils from '../utils'
import {
    DEVICE_MAC,
    DEVICE_UUID,
    C781_CONTROL_SERVICE,
    CHAR_ID_TO_SUBSCRIBE_FOR_DATA,
    CHAR_TO_WRITE_COMMANDS_TO,
    C781_RECIPES_PATH
} from './c781_config';

interface DeviceStatus {
    weight: string;
    water: string;
    pageN: string;
    [key: string]: string | (() => string);
    toString(): string;
}

interface MenuItem {
    text: string;
    action: () => Promise<void>;
}



function init(): void {
    ui.init(get_main_menu_items_list(), commands_menu.get_commands_menu_items_list(), treat_keys_callback);
    init_keys_action();
    commands_menu.init(closing_submenu_callback);
    ui.ui_elements.main_menu_list.focus();
}



const main_menu = {
    line_items: [
        {text: 'Connect to C781 by hardcoded MAC (uuid for apple))',   action: connect_by_mac},
        {text: 'Connect to nearest C781',                              action: connect_nearest},
        {text: 'Connect - pick from list',                             action: connect_to_device_from_list},
        {text: '',                                                     action: empty_action},
        {text: 'Commands ►',                                           action: commands_submenu},
        {text: 'Recipies ►',                                           action: run_recipes},
        {text: 'Set auto recipe',                                      action: set_auto_recipe},
        {text: '',                                                     action: empty_action},
        {text: '-- UTILS --',                                          action: empty_action},
        {text: 'Send raw data',                                        action: utils.send_raw_data},
        {text: 'Clear logs',                                           action: clear_logs},
        {text: 'Scan BLE',                                             action: scan_ble},
        {text: 'Scan services and chars (connect first ^^)',           action: ble.main.discover_services_and_chars},
        {text: 'Subscribe to data',                                    action: subscribe_to_data}
    ] as MenuItem[]
};

const BLE_SCAN_TIME = 10;

const device_status: DeviceStatus = {
    weight: '',
    water: '',
    pageN: '',

    toString: function(): string {
        if (this) {
            return Object.entries(this)
                .filter(([key]) => key !== 'toString') // Exclude 'toString' from the output
                .map(([key, value]) => `${key}: ${typeof value === 'string' ? value : ''}`).join(', ');
        }
        return '';
    }
};



/*****************************
 *    action keys
 ****************************/
function init_keys_action() : void {
    ui.ui_elements.main_menu_list.on('select', async function(selected_item: { content: string }) {
        const itm = main_menu.line_items.find(line => line.text === selected_item.content);
        if (itm && typeof itm.action === 'function') {
            await itm.action();
        } else {
            throw new Error(`Action not found for item: ${selected_item.content}`);
        }
    });
}


function treat_keys_callback(active_window: any, key: string): void {
    if((active_window === ui.ui_elements.main_menu_list || active_window === ui.ui_elements.logsBox) && key === 'escape') {
        const conn_status = ble.main.get_connection_status();
        if(conn_status === 'connecting') {
            ble.main.stop_scanning();
            ui.logContent("stopping scanning...");
        } else if(conn_status === "connected") {
            ble.main.disconnect();
            ui.logContent("disconnected");
        } else {
            return process.exit(0);
        }
    }
}



/************************************
 *    main menu items actions
 ***********************************/
async function connect_by_mac(): Promise<void> {
    ui.logContent('поиск C781...');
    await utils.connect_by_mac(DEVICE_MAC, DEVICE_UUID, CHAR_TO_WRITE_COMMANDS_TO, CHAR_ID_TO_SUBSCRIBE_FOR_DATA, data_from_device_cb);
    // эта команда - не запрос на изменение а сообщение что изменение состоялось
    // библиотека nodejs abandon noble инициацию изменения MTU со своей стороны не поддерживает.. но вроде и так все работает
    await ble.main.send_data(raw_commands.NegotiatingMtuDone());
}



async function connect_nearest(): Promise<void> {
    ui.logContent('Поиск ближайшего C781...');
    await utils.connect_to_nearest(null, C781_CONTROL_SERVICE, CHAR_TO_WRITE_COMMANDS_TO, CHAR_ID_TO_SUBSCRIBE_FOR_DATA, data_from_device_cb);

    // эта команда - не запрос на изменение а сообщение что изменение состоялось
    // библиотека nodejs abandon noble инициацию изменения MTU со своей стороны не поддерживает.. но вроде и так все работает
    await ble.main.send_data(raw_commands.NegotiatingMtuDone());
}



async function connect_to_device_from_list(): Promise<void> {
    logContent('Поиск D707...');
    await utils.pick_from_list_and_connect(null, C781_CONTROL_SERVICE, CHAR_TO_WRITE_COMMANDS_TO, CHAR_ID_TO_SUBSCRIBE_FOR_DATA, data_from_device_cb);
        // эта команда - не запрос на изменение а сообщение что изменение состоялось
    // библиотека nodejs abandon noble инициацию изменения MTU со своей стороны не поддерживает.. но вроде и так все работает
    await ble.main.send_data(raw_commands.NegotiatingMtuDone());
}



async function commands_submenu(): Promise<void> {
    ui.ui_elements.main_menu_list.hide();
    ui.ui_elements.submenu_list.show();
    ui.ui_elements.submenu_list.focus();
    ui.ui_elements.screen.render();
}



async function run_recipes(): Promise<void> {
    const recipe_to_run = await pick_recipe_popup();
    if (!recipe_to_run) return;

    run_recipe(recipe_to_run);
}

async function set_auto_recipe(): Promise<void> {
    const slot = await ui.input_popup(' enter slot number (1,2,3)', '1', 'int') as number;
    if (slot < 1 || slot > 3)
    {
        logContent('wrong slot number');
        return;
    }

    const bypass = await ui.input_popup(' enter bypass volume (0-255)', '0', 'int') as number;
    if (bypass < 0 || bypass > 255) 
    {
        logContent('wrong bypass volume');
        return;
    }

    const recipe_to_set = await pick_recipe_popup();
    if (!recipe_to_set) return;

    await send_to_device(commands.make_command(commands.commands_list.RD_EASYMODE_RECIPE_SEND, 
                                slot-1,
                                bypass,
                                "Xdripper",            // 'Xpod', 'Xdripper', 'Other', 'Tea'.   Tea is not implemented - not clear how it should work
                                0,                     // scale on off (0-1)
                                0,                     // tea pour qty (0-7) ???
                                recipes.get_recipe_block(recipe_to_set.pouring_steps),
                                recipe_to_set.pouring_steps.grind_size,
                                recipe_to_set.pouring_steps.ratio));

    logContent ("!!! НАДО ОТПРАВИТЬ ТРИ РЕЦЕПТА !!!");
}



async function clear_logs(): Promise<void> {
    ui.clear_logs();
}



async function scan_ble(): Promise<void> {
    ble.main.scan_all(BLE_SCAN_TIME);
}



async function subscribe_to_data(): Promise<void> {
    await ble.main.subscribe_to(CHAR_ID_TO_SUBSCRIBE_FOR_DATA, data_from_device_cb);
}



/************************************
 *    other functions
 ***********************************/
function get_main_menu_items_list(): string[] {
    return main_menu.line_items.map(item => item.text);
}



function closing_submenu_callback(): void {
    ui.ui_elements.main_menu_list.show();
    ui.ui_elements.main_menu_list.focus();
    ui.ui_elements.screen.render();
}



async function pick_recipe_popup(): Promise<any> {
    const files = await recipes.getRecipiesFilesList();
    const picked_item = await ui.show_select_popup(files, ' Select recipe ');
    ui.ui_elements.main_menu_list.focus();
    
    if (picked_item === null) 
    {
        logContent('no recipe selected');
        return null;
    }

    logContent('file name is: '.concat(picked_item.content));
    try {
        const filePath = path.join(__dirname, C781_RECIPES_PATH, picked_item.content);
        const fileContent = await fs.promises.readFile(filePath, 'utf-8');
        const recipeData = JSON.parse(fileContent);
        // logContent('Parsed recipe data: ' + JSON.stringify(recipeData, null, 2));
        return recipeData;
    } catch (error) {
        logContent(`Error reading or parsing file: ${(error as Error).message}`);
    }
    return null
}



async function run_recipe(recipe: any): Promise<void> {
    try {

        logContent('Running recipe \n      bypass volume = '.concat(recipe.bypass_volume.toString(), '\n      bypass temperature = '.concat(recipe.bypass_temperature.toString(), '\n         grind weight = '.concat(recipe.grind_weight.toString()))));
        logContent(recipes.recipe_to_string(recipe.pouring_steps));

        await send_to_device(recipes.get_main_recipes_settings_block(recipe.bypass_volume, recipe.bypass_temperature, recipe.grind_weight));
        let result = await wait_for_reply(commands.commands_list.REPLY_TO_SET_BYPASS.code) as any;
        if (!result) 
        {
            logContent('recipe step 1 failed');
            return;
        }
        logContent ("first step passed");

        await send_to_device(recipes.get_cup_type_block(110, 90));
        result = await wait_for_reply(commands.commands_list.REPLY_TO_SET_CUP_TYPE.code);
        if (!result) 
        {
            logContent('recipe step 2 failed');
            return;
        }
        logContent ("second step passed");

        await send_to_device(recipes.get_recipe_block(recipe.pouring_steps));
        result = await wait_for_reply(commands.commands_list.REPLY_TO_SET_RECIPE.code);
        if (!result) 
        {
            logContent('recipe step 3 failed');
            return;
        }
        logContent ("third step passed");

        await send_to_device(recipes.get_recipe_start_block());
        result = await wait_for_reply(commands.commands_list.REPLY_TO_EXEC_RECIPE.code);
        if (!result) 
        {
            logContent('recipe step 4 failed');
            return;
        }
        logContent ("RECIPE STARTED");



    } catch (error) {
        logContent(`Error running Recipe: ${(error as Error).message}`);
    }
}



async function wait_for_reply(code: number): Promise<void> {
    for (let i = 0; i < 10; i++) 
    {
        const result = await ble.low_level.waitForNextMessage();
        const command_code = result[4]*0x100 + result[3];
        if(command_code === code) return result;        
    }

    return;
}



async function send_to_device(data: Buffer): Promise<void> {
    if (ble.main.get_connection_status() !== 'connected')
    {
        ui.logContent('Not connected to device');
        return;
    }

    try {
        await ble.main.send_data(data);
    } catch (error) {
        ui.logContent(`Error sending data: ${error}`);
    }
}

function data_from_device_cb(data: Buffer): void {
    const command_code = data[4]*0x100 + data[3];
    // if its weight data   580207 1550 10000000 c1 00000000 16b5
    if (command_code === commands.commands_list.RD_POURED_WATER_DATA.code)
    {
        const floatValue = data.readFloatLE(10);
        device_status.weight = Math.round(floatValue).toString();
        ui.updateStatusBar([device_status.toString()]);
    } else 
    
    // poured water data   580207 4b9e 10000000 c1 00000000 fd32 
    if (command_code === commands.commands_list.RD_WEIGHT_DATA.code)
    {
        const floatValue = data.readFloatLE(10);
        device_status.water = Math.round(floatValue).toString();
        ui.updateStatusBar([device_status.toString()]);
    } else {
        logContent("    >> raw incoming data: ".concat(data.toString('hex')));
        logContent("    >> incoming block name: ".concat(commands.get_command_name_by_code(command_code) as string, " code: ", command_code.toString()));

        const parsed = telemetry_parser.parse_data(data);
        logContent("    >> telemetry parsed: ".concat(parsed.toString()));
        
        if (command_code === commands.commands_list.RD_MachineInfo.code)
        {
            logContent("machine data:  ".concat(JSON.stringify(parsed.machine_data)));
        }

        if (command_code === commands.commands_list.U_PAGE_NUMBER.code)
        {
            device_status.pageN = parsed.values[1];
        }

        logContent('');
        logContent('');
    }
}

async function empty_action(): Promise<void> {}

const logContent = ui.logContent;

// Need to import raw-commands with named imports
import * as raw_commands from './raw-commands';

export { init , logContent}; 