interface ConnectionStatus {
    wifiConnectionStatus: number;
    wifiSignalStrength: number;
    systemConnectionStatus: number;
    otaStatus: number;
    as_string: string;
}

interface ConnectionStatusError {
    error: string;
}

function parseConnectionStatus(buffer: Buffer): ConnectionStatus | ConnectionStatusError {
    if (buffer.length < 4) {
        return ({error: 'Invalid buffer provided. Buffer must be a >= 4-bytes buffer.'});
    }

    const wifiConnectionStatus          = buffer.readUInt8(0);
    const wifiSignalStrength            = buffer.readUInt8(1);
    const systemConnectionStatus        = buffer.readUInt8(2);
    const otaStatus                     = buffer.readUInt8(3);
    const as_string = `wifi: ${wifiConnectionStatus === 0 ? "disconnected" : "connected"}, \
    signal: ${wifiSignalStrength}, \
    system: ${systemConnectionStatus === 0 ? "disconnected" : "connected"}, \
    OTA: ${otaStatus}`;

    return {
        wifiConnectionStatus,
        wifiSignalStrength,
        systemConnectionStatus,
        otaStatus,
        as_string
    };
}

export {
  parseConnectionStatus,
} 