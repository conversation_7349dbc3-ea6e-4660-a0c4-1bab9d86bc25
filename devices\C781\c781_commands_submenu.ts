// сдесь пункты и логика подменю "команды"
import ble from '../../BLE/ble';

import * as commands from './c781_commands';
import * as ui from '../../UI/ui_common_logic';
import { Command, CommandParameter } from '../CommandTypes/command_types';
import * as utils from '../utils';



interface MenuItem {
    text: string;
    action: Command | string;
}

const commands_list = commands.commands_list;

const commands_submenu = {
    line_items: [
        { text: '← Return',             action: "return" }, 
        { text: '--- BREWER ---',       action: "no action"},
        { text: '  Brewer IN',          action: commands_list.BREWER_IN },
        { text: '  Start brew',         action: commands_list.BREWER_START },
        { text: '  Brewer pause',       action: commands_list.BREWER_PAUSE },
        { text: '  Brewer restart',     action: commands_list.BREWER_RESTART },
        { text: '  Stop brew',          action: commands_list.BREWER_STOP },
        { text: '  Water source',       action: commands_list.BREWER_WATER_SOURCE },
        { text: '  Set temperature',    action: commands_list.BREWER_SET_TEMPERATURE },
        { text: '  Quit brewer',        action: commands_list.BREWER_QUIT },
        { text: '--- GRINDER ---',      action: "no action"},
        { text: '  Set grinder params', action: commands_list.IN_TO_GRINDER_MODE_AND_SET_SIZE_AND_SPEED },
        { text: '  Start grinder',      action: commands_list.START_GRINDING },
        { text: '  Stop grinder',       action: commands_list.STOP_GRINDING },
        { text: '  Calibrate grinder',  action: commands_list.CALIBRATE_GRINDER },
        { text: '  Grinder quit',       action: commands_list.GRINDER_QUIT },
        { text: '--- SCALE ---',        action: "no action"},
        { text: '  enter scale',        action: commands_list.SCALE_ENTER },
        { text: '  exit scale',         action: commands_list.SCALE_EXIT },
        { text: '  reset scale',        action: commands_list.SCALE_RESET },
        { text: '--- CUP SCREW ---',    action: "no action"},
        { text: '  Move cup right',     action: commands_list.CUP_RIGHT },
        { text: '  Move cup left',      action: commands_list.CUP_LEFT },
        { text: '  Shake cup',          action: commands_list.CUP_SHAKE },
        { text: '  One step right',     action: commands_list.CUP_STEP_RIGHT },
        { text: '  One step left',      action: commands_list.CUP_STEP_LEFT },
        { text: '  Stop',               action: commands_list.CUP_STOP },
        { text: '--- UTILS ---',        action: "no action"},
        { text: '  set mode easy/pro',  action: commands_list.UTILS_SET_MODE },
        { text: '  set weight units',   action: commands_list.UTILS_SET_WEIGHT_UNITS },
        { text: '  brightness',         action: commands_list.UTILS_SET_BRIGHTNESS },
        { text: '  temperature units',  action: commands_list.UTILS_SET_TEMPERATURE_UNITS }
    ] as MenuItem[]
};

let closing_submenu_callback: (() => void) | null = null;

// default parameters values
set_default_value_for_parameter('temperature', 50);
set_default_value_for_parameter('rotation', 1);
set_default_value_for_parameter('water source', 1);
set_default_value_for_parameter('flow rate', 2);
set_default_value_for_parameter('water volume', 40);
set_default_value_for_parameter('grind size', 40);
set_default_value_for_parameter('grind speed', 90);
set_default_value_for_parameter('grinder adjustment speed', 1200);
set_default_value_for_parameter('mode', 0);
set_default_value_for_parameter('mode?', 0);
set_default_value_for_parameter('weight units', 0);
set_default_value_for_parameter('brightness', 16);
set_default_value_for_parameter('temperature units', 0);

function init(submenu_closed_callback: () => void): void {
    closing_submenu_callback = submenu_closed_callback;
    init_keys_action();
}

/*****************************
 *    action keys
 ****************************/
function init_keys_action() : void {
    ui.ui_elements.submenu_list.on('select', async function(selected_item: {content: string}) {
        const itm = commands_submenu.line_items.find(line => line.text === selected_item.content);
        if (!itm) return;

        switch (itm.action) {
            case "return":
                ui.ui_elements.submenu_list.hide();
                if (closing_submenu_callback) closing_submenu_callback();
                break;
        
            case "no action":
                // do nothing
                break;

            default:
                {
                const commandAction = itm.action as Command;
                if (commandAction.params_qty > 0) {
                    const params = await utils.ask_user_for_parameters(commandAction);
                    if(params === null) return;
                    ui.logContent(`sending to device: ${selected_item.content} `);
                    await send_to_device(commands.make_command(itm.action as Command,
                        ...params
                    ));
                } else {
                    ui.logContent(`sending to device: ${selected_item.content} `);
                    await send_to_device(commands.make_command( itm.action as Command));
                }
                }
                break;
        }
    });
}


/************************************
 *    other functions
 ***********************************/
function set_default_value_for_parameter(param_name: string, value: number): void {
    for (const command_key in commands_list) {
        const command = commands_list[command_key];
        for (let i = 1; i <= command.params_qty; i++) {
            const param_key = `p${i}` as keyof Command;
            const param = command[param_key] as CommandParameter | undefined;
            if (param && param.name === param_name) {
                (param as any).default_value = value;
            }
        }
    }
}

function get_commands_menu_items_list(): string[] {
    return commands_submenu.line_items.map(item => item.text);
}

async function send_to_device(data: Buffer): Promise<void> {
    await ble.main.send_data(data);
}

export { init, get_commands_menu_items_list }; 