/**
 * BLE 5.0 Enhanced Features Example
 * 
 * This example demonstrates how to use the new BLE 5.0 features
 * including extended advertising, PHY selection, and enhanced scanning.
 */

import ble from './ble';
import { BLE5Config, PHYType } from './ble';

// Example logging function
function log(message: string, color?: string): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${message}`);
}

async function ble5Example() {
    try {
        // Initialize BLE with BLE 5.0 configuration
        const ble5Config: Partial<BLE5Config> = {
            preferredPHY: '2M',           // Use 2M PHY for higher data rates
            extendedAdvertising: true,     // Enable extended advertising
            codedPHYSupport: true,        // Enable Coded PHY for long range
            periodicAdvertising: false,    // Disable periodic advertising for now
            advertisingInterval: 50,       // 50ms advertising interval
            maxAdvertisingDataLength: 1650 // Maximum BLE 5.0 advertising data
        };

        log('Initializing BLE with BLE 5.0 features...');
        await ble.main.init_ble(log, ble5Config);

        // Get current BLE 5.0 configuration
        const currentConfig = ble.ble5.get_ble5_config();
        log(`Current BLE 5.0 Config: ${JSON.stringify(currentConfig, null, 2)}`);

        // Start enhanced BLE 5.0 scanning
        log('Starting BLE 5.0 enhanced scanning...');
        await ble.ble5.start_ble5_scanning([], {
            allowDuplicates: false,
            scanPHY: '2M',              // Scan on 2M PHY
            extendedScan: true,         // Use extended scanning
            activeScan: true            // Active scanning for scan response
        });

        // Wait for devices to be discovered
        setTimeout(async () => {
            await ble.main.stop_scanning();
            log('Scanning stopped');
        }, 10000); // Scan for 10 seconds

    } catch (error) {
        log(`Error: ${(error as Error).message}`, 'red');
    }
}

async function connectAndTestBLE5Features() {
    try {
        // Find a device by name (replace with your device name)
        log('Looking for BLE 5.0 device...');
        const devices = await ble.main.find_all_by_name('YourDeviceName');
        
        if (!devices || devices.length === 0) {
            log('No devices found');
            return;
        }

        const device = devices[0];
        log(`Found device: ${device.advertisement.localName} (${device.address})`);

        // Check BLE 5.0 support
        const ble5Support = ble.ble5.check_ble5_support(device);
        log(`BLE 5.0 Support: ${JSON.stringify(ble5Support, null, 2)}`);

        // Connect to the device
        log('Connecting to device...');
        await ble.main.connect(device);
        log('Connected successfully');

        // Get connection parameters
        const connectionParams = ble.ble5.get_connection_parameters(device);
        log(`Connection Parameters: ${JSON.stringify(connectionParams, null, 2)}`);

        // Try to update PHY if supported
        if (ble5Support.phySupport.includes('2M')) {
            log('Attempting to update PHY to 2M...');
            const phyUpdateSuccess = await ble.ble5.update_connection_phy(device, '2M', '2M');
            log(`PHY Update Result: ${phyUpdateSuccess ? 'Success' : 'Failed'}`);
        }

        // Disconnect after testing
        setTimeout(async () => {
            await ble.main.disconnect();
            log('Disconnected from device');
        }, 5000);

    } catch (error) {
        log(`Connection Error: ${(error as Error).message}`, 'red');
    }
}

async function demonstrateBLE5Config() {
    // Demonstrate configuration updates
    log('=== BLE 5.0 Configuration Demo ===');
    
    // Get initial config
    let config = ble.ble5.get_ble5_config();
    log(`Initial Config: ${JSON.stringify(config, null, 2)}`);

    // Update configuration for long-range operation
    log('Updating config for long-range operation...');
    ble.ble5.update_ble5_config({
        preferredPHY: 'Coded',        // Use Coded PHY for long range
        codedPHYSupport: true,        // Enable Coded PHY
        advertisingInterval: 200,     // Longer interval for power saving
        extendedAdvertising: true     // Keep extended advertising
    });

    config = ble.ble5.get_ble5_config();
    log(`Updated Config: ${JSON.stringify(config, null, 2)}`);

    // Update configuration for high-speed operation
    log('Updating config for high-speed operation...');
    ble.ble5.update_ble5_config({
        preferredPHY: '2M',           // Use 2M PHY for high speed
        advertisingInterval: 20,      // Faster advertising
        maxAdvertisingDataLength: 1650 // Maximum data length
    });

    config = ble.ble5.get_ble5_config();
    log(`High-Speed Config: ${JSON.stringify(config, null, 2)}`);
}

// Main execution
async function main() {
    log('=== BLE 5.0 Enhanced Features Demo ===');
    
    // Run configuration demo
    await demonstrateBLE5Config();
    
    // Run basic BLE 5.0 example
    await ble5Example();
    
    // Uncomment to test connection features (requires actual BLE 5.0 device)
    // await connectAndTestBLE5Features();
}

// Export for use in other modules
export {
    ble5Example,
    connectAndTestBLE5Features,
    demonstrateBLE5Config
};

// Run if this file is executed directly
if (require.main === module) {
    main().catch(error => {
        log(`Fatal Error: ${error.message}`, 'red');
        process.exit(1);
    });
}
