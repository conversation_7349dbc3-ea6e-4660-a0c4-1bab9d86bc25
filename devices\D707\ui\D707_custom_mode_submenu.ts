// сдесь пункты и логика подменю "команды"
import ble              from '../../../BLE/ble';
import * as commands    from '../D707_commands';
import * as ui          from '../../../UI/ui_common_logic';



const pr = commands.device_parameters as any;

interface LineItem {
    text: string;
    action: any;
    original_length?: number;
}

interface CustomModeSubmenu {
    line_items: LineItem[];
}

const custom_mode_submenu: CustomModeSubmenu = {
    line_items: [
        { text: '← Return',             action: "return" }, 
        { text: '  Send',               action: "send" }, 
        { text: '',                     action: null, }, 
        { text: '--- PARAMETERS ---',   action: null},
        { text: '  state',              action: pr.state },
        { text: '  mode',               action: pr.mode },
        { text: '  left force',         action: pr.left_force },    
        { text: '  left direction',     action: pr.left_dir },
        { text: '  right force',        action: pr.right_force },
        { text: '  right direction',    action: pr.right_dir },
        { text: '  front heat',         action: pr.front_heat },
        { text: '  back heat',          action: pr.back_heat },
        { text: '  massage spot',       action: pr.massage_spot },
        { text: '  reciprocating mode', action: pr.recipr_mode },
        { text: '  time',               action: pr.time },
    ]
};

// save original length or items text
custom_mode_submenu.line_items.forEach(item => {
    item.original_length = item.text.length;
});

pr.state.value = 0;
pr.mode.value = 1;
pr.left_force.value = 1; 
pr.left_dir.value = 1;
pr.right_force.value = 1; 
pr.right_dir.value = 1;
pr.front_heat.value = 1;
pr.back_heat.value = 1;
pr.massage_spot.value = 1; 
pr.recipr_mode.value = 1;
pr.time.value = 300;

let closing_submenu_callback: () => void | null = null;

function init(submenu_closed_callback: () => void): void {
    closing_submenu_callback = submenu_closed_callback;
    init_keys_action();
    update_submenu_items_text();
}



/*****************************
 *    action keys
 ****************************/
function init_keys_action() : void {
    ui.ui_elements.submenu_list.on('select', async function (selected_item: { index: number, content: string }) {
        const itm = custom_mode_submenu.line_items[selected_item.index - 3];
        if (!itm) throw new Error('err: line item not found');

        // ui.logContent("===".concat(selected_item.index.toString(), "   ",selected_item.content));
        switch (itm.action) {
            case "return":
                ui.ui_elements.submenu_list.hide();
                if (closing_submenu_callback) closing_submenu_callback();
                break;
        
            case null:
                // do nothing
                break;

            case 'send':
                ble.main.send_data(
                    commands.get_command_custom_mode( 2,
                        pr.state.options[pr.state.value],
                        pr.mode.options[pr.mode.value],
                        pr.left_force.options[pr.left_force.value],
                        pr.left_dir.options[pr.left_dir.value],
                        pr.right_force.options[pr.right_force.value],
                        pr.right_dir.options[pr.right_dir.value],
                        pr.front_heat.options[pr.front_heat.value],
                        pr.back_heat.options[pr.back_heat.value],
                        pr.massage_spot.options[pr.massage_spot.value],
                        pr.recipr_mode.options[pr.recipr_mode.value],
                        pr.time.value,
                        true
                    )
                );
                break;

            default:
                {
                    const input = await ask_user_for_parameter(itm.action);
                if (input === null) return;
                
                itm.action.value = input;
                update_submenu_items_text();
                }
                break;
        }
    });
}



// doesnt use function from utils ue to different param structure
async function ask_user_for_parameter(command: any): Promise<number | null> {
    if (!Object.values(commands.device_parameters).includes(command)) {
        ui.logContent("command not found in available commands list");
        throw new Error('command not found in available commands list');
    }

    let user_input: number | string | null = null;

    if (command.type === 'int' || command.type === 'float') {   
        try {
            user_input = await ui.input_popup(' enter '.concat(command.name, ' in range: ', command.min.toString(), ' - ', command.max.toString(), "кратно 300?"), 
                                            command.value.toString(),
                                            command.type as 'int' | 'float');
        } catch {
            ui.logContent("user input error");
            return null;
        }
    } else if (command.type === 'enum') {
        const user_picked_option = await ui.show_select_popup(command.options, ' pick '.concat(command.name, ' option'));
        user_input = user_picked_option.index;
    } else {
        ui.logContent("wrong data type:".concat(command.type));
        return null;
    }
    
    return user_input as number | null;
}

// /************************************
//  *    other functions
//  ***********************************/
function update_submenu_items_text(): void {
    const active_line = ui.ui_elements.submenu_list.selected;

    const valueOffset = 25; // Adjust this value to control the offset
    custom_mode_submenu.line_items.forEach(item => {
        if (typeof item.action != 'object' || item.action === null) {
            // Do nothing for non-parameter items
        } else {
            item.text = item.text.slice(0, item.original_length);
            
            const spacesToAdd = valueOffset - item.text.length;
            if (spacesToAdd > 0) {
                item.text = item.text.padEnd(item.original_length + spacesToAdd, ' ');
            } else {
                item.text = item.text.padEnd(valueOffset, ' ');
            }
            
            if (item.action.data_type === 'enum') {
                item.text += item.action.options[item.action.value];
            } else {
                item.text += item.action.value.toString();
            }
        }
    });
    ui.ui_elements.submenu_list.clearItems();
    ui.ui_elements.submenu_list.setItems(custom_mode_submenu.line_items.map(item => item.text));
    ui.ui_elements.submenu_list.select(active_line);
    ui.ui_elements.screen.render();
}

function get_custom_mode_submenu_items_list(): string[] {
    return custom_mode_submenu.line_items.map(item => item.text);
}

export { init, get_custom_mode_submenu_items_list }; 