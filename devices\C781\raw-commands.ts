// raw commands to send to coffee machine
const NEGOTIATE_MTU:    string = "580101A41F1000000001010000000243";
const GRIND_START:      string = "580101AC0D1800000001E80300004F0000003C000000BCFD";
const GRIND_STOP:       string = "580101B10D0C00000001A6BA";
const GET_DEVICE_INFO:  string = "580101B30D0C000000014000";
const GET_MACHINE_INFO: string = "580101A90D0C000000010000";
const HEART_BEAT:       string = "580101AB0D0C000000010000";
const GRIND_IN:         string = "580101B20D0C000000010000";
const MACHINE_ACTIVITY: string = "580101B00D0C000000010000";
const CHANGE_UNITS:     string = "580101AF0D0C000000010000";    



function NegotiatingMtuDone(): Buffer {
    return hexStringToBuffer(NEGOTIATE_MTU);
}

function GrindStart(): Buffer {
    return hexStringToBuffer(GRIND_START);
}

function GrindStop(): Buffer {
    return hexStringToBuffer(GRIND_STOP);
}

function DeviceInfo(): Buffer {
    return hexStringToBuffer(GET_DEVICE_INFO);
}

function MachineInfo(): Buffer {
    return hexStringToBuffer(GET_MACHINE_INFO);
}

function HeartBeat(): Buffer {
    return hexStringToBuffer(HEART_BEAT);
}

function GrindIn(): Buffer {
    return hexStringToBuffer(GRIND_IN);
}

function MachineActivity(): Buffer {
    return hexStringToBuffer(MACHINE_ACTIVITY);
}

function ChangeUnits(): Buffer {
    return hexStringToBuffer(CHANGE_UNITS);
}

function hexStringToBuffer(hexString: string): Buffer {
    return Buffer.from(hexString, 'hex');
}

function bufferToHexString(buffer: Buffer): string {
    return buffer.toString('hex');
}



export {
    NegotiatingMtuDone,
    GrindStart,
    GrindStop,
    DeviceInfo,
    MachineInfo,
    HeartBeat,
    GrindIn,
    MachineActivity,
    ChangeUnits,
    bufferToHexString,
    hexStringToBuffer
}
