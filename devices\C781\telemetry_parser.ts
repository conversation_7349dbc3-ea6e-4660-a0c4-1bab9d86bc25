// структура пакетов:
// все входящие пакеты начинаются с 580207 затем идет код команлы 4A1F затем длина пакета(целиком) 0C 000000 C1291C  
// последние 4 байта - CRC

import { commands_list, get_command_by_code } from './c781_commands';
import ble from '../../BLE/ble';

interface ParseResult {
    command_code: string;
    data: string;
    crc: string;
    crc_check: string;
    error: string;
    values: any[];
    machine_data?: any;
    toString(): string;
}

interface MachineInfo {
    serialNumber: string;
    theModel: string;
    theVersion: string;
    areaAp: number;
    waterEnough: number;
    systemStatus: number;
    userCount: number;
    waterFeed: number;
    grinder: number;
    ledType: number;
    voltage: number;
    tempUnit: number;
    weightUnit: number | string;
    modeType: number;
}

// data -expect buffer
function parse_data(data: Buffer): ParseResult {
    const result: ParseResult = {
        command_code: '',
        data: '',
        crc: '',
        crc_check: '',
        error: '',
        values: [],
        // machine_data: '',
        toString: function() {
            return `Command Code: ${this.command_code}` +
                    `  Data: ${this.data}` +
                    `  CRC: ${this.crc}` + 
                    `  CRC: ${this.crc_check}` +
                    (this.error ? ` Error: ${this.error}` : '') + '\n' +
                    (this.values.length != 0 ? '                          Values: ' + this.values.join('') : '');
        }
    };

    if (!Buffer.isBuffer(data)) {
        result.error = "Data is not a buffer";
        return result;
    }

    const hexString = data.toString('hex').toUpperCase();

    // Извлечение длины пакета
    let lengthHex = hexString.substring(10, 18);
    // Reverse byte order for length (4 bytes)
    lengthHex = lengthHex.substring(6, 8) + lengthHex.substring(4, 6) + lengthHex.substring(2, 4) + lengthHex.substring(0, 2);

    const length = parseInt(lengthHex, 16) * 2;
    // Проверка длины пакета
    if (hexString.length != length) {
        result.error = "wrong length" + length + "  " + hexString.length;
        return result;
    }

    // Извлечение кода команды
    let commandCodeHex = hexString.substring(6, 10);
    commandCodeHex = commandCodeHex.substring(2, 4) + commandCodeHex.substring(0, 2);
    const commandCode = parseInt(commandCodeHex, 16);

    // Извлечение данных
    const dataHex = hexString.substring(18, length - 4);

    // Извлечение CRC (2 bytes)
    const crcHex = hexString.substring(length - 4, length - 0);
    // CRC check
    const data_bytes = Buffer.from(hexString.substring(0, length - 4), 'hex');
    const crc_calculated = ble.utils.calculate_crc16_modbus(data_bytes);
    const crc_calculated_hex = crc_calculated.toString('hex').toUpperCase();
    if (crc_calculated_hex !== crcHex) {
        result.error = "CRC check failed: real: " + crcHex + " calculated: " + crc_calculated_hex;
        result.crc_check = "wrong";
        return result;
    }

    result.command_code = commandCode.toString();
    result.data = dataHex.toString();
    result.crc = crcHex.toString();
    result.crc_check = "ok";
    
    // check if its existing command code
    const command = get_command_by_code(commandCode);
    if (!command) {
        result.error = "Unknown command code: " + commandCode;
        return result;
    } 
        
    // parse machine info
    if(commandCode === commands_list.RD_MachineInfo?.code) {
        result.machine_data = parseMachineInfoData(dataHex);
        return result;
    } else {
        result.machine_data = commandCode;
    }
    
    // parse values
    const dataValues: any[] = [];
    for (let i = 2; i < dataHex.length; i += 8) {    // 2 is shift coz data block has constant C1 header at the start 
        const block = dataHex.substring(i, i + 8);
        const block_number = 1 + (i - 2)/8; 
        if (block.length === 8) {
            const reversedBlock = block.substring(6, 8) + block.substring(4, 6) + block.substring(2, 4) + block.substring(0, 2);
            
            if(block_number > command.params_qty) {
                result.error = 'too many values';
                continue;
            }

            const param_key = `p${block_number}` as keyof typeof command;
            const param = command[param_key] as any;

            if (param && param.type === 'int') {
                dataValues.push(param.name + " = ");
                dataValues.push(parseInt(reversedBlock, 16));
            } else if(param && param.type === 'float') {
                dataValues.push(param.name + " = ");
                dataValues.push(parseInt(reversedBlock, 16) + ' | ');
            }
        }
    }
    result.values = dataValues;

    return result;
}

function parseMachineInfoData(data: string): MachineInfo {
    // Function to reverse a hex string
    const reverseHex = (hex: string): string => {
        return hex.match(/.{1,2}/g)?.reverse().join('') || '';
    };

    try {
        const result = {} as MachineInfo;

        // Parsing according to the structure in MachineInfoBleModel
        result.serialNumber = Buffer.from(data.substr(0, 26), 'hex').toString('ascii').replace(/\0/g, '');
        result.theModel = Buffer.from(data.substr(26, 12), 'hex').toString('ascii').replace(/\0/g, '');
        result.theVersion = Buffer.from(data.substr(38, 20), 'hex').toString('ascii').replace(/\0/g, '');

        // Parsing numeric values
        const areaAp_hex = reverseHex(data.substr(58, 8));
        result.areaAp = areaAp_hex ? Buffer.from(areaAp_hex, 'hex').readFloatBE(0) : 0;

        result.waterEnough = data.length >= 68 ? parseInt(reverseHex(data.substr(66, 2)), 16) : 0;
        result.systemStatus = data.length >= 70 ? parseInt(reverseHex(data.substr(68, 2)), 16) : 0;
        result.userCount = data.length >= 72 ? parseInt(reverseHex(data.substr(70, 2)), 16) : 0;
        result.waterFeed = data.length >= 74 ? parseInt(reverseHex(data.substr(72, 2)), 16) : 0;

        if (data.length >= 76) {
            const grinder_value = parseInt(reverseHex(data.substr(74, 2)), 16);
            result.grinder = Math.max(1, grinder_value - 30);
        } else {
            result.grinder = 1;
        }
        
        result.ledType = data.length >= 78 ? parseInt(reverseHex(data.substr(76, 2)), 16) : 0;
        result.voltage = data.length >= 80 ? parseInt(reverseHex(data.substr(78, 2)), 16) : 0;
        result.tempUnit = data.length >= 82 ? parseInt(reverseHex(data.substr(80, 2)), 16) : 0;

        // Weight unit (if available)
        if (data.length >= 84) {
            result.weightUnit = parseInt(reverseHex(data.substr(82, 2)), 16);
        } else {
            result.weightUnit = 'g'; // Default weight unit to grams
        }

        // Mode type (EASY/PRO)
        if (data.length >= 110) {
            const mode_code = data.substr(102, 8);
            // In Java, checking against DeviceMode.EASY.getCode()
            result.modeType = mode_code === "EASY_MODE" ? 1 : 0; // 1 = EASY, 0 = PRO
        } else {
            result.modeType = 0; // Default to PRO mode
        }

        return result;
    } catch (e) {
        throw new Error(`Error parsing machine info: ${e}`);
    }
}

export { parse_data, ParseResult, MachineInfo }; 