declare module 'blessed' {
    namespace blessed {
        namespace Widgets {
            interface BoxOptions {
                parent?: any;
                top?: number | string;
                left?: number | string;
                right?: number | string;
                bottom?: number | string;
                width?: number | string;
                height?: number | string;
                content?: string;
                tags?: boolean;
                border?: any;
                style?: any;
                scrollable?: boolean;
                alwaysScroll?: boolean;
                scrollbar?: any;
                keys?: boolean;
                vi?: boolean;
                mouse?: boolean;
                hidden?: boolean;
                label?: string;
                bg?: string;
            }

            interface ListOptions extends BoxOptions {
                items?: string[];
            }

            interface ListElement {
                on(event: string, callback: Function): void;
                key(keys: string[], listener: (...args: any[]) => void): void;
                addItem(item: string): void;
                clearItems(): void;
                setItems(items: string[]): void;
                focus(): void;
                hide(): void;
                show(): void;
                hidden: boolean;
                selected: number;
                removeItem(item: string): void;
                removeListener(event: string, callback: Function): void;
                removeKey(keys: string[], listener: (...args: any[]) => void): void;
                setLabel(label: string): void;
                select(index: number): void;
            }

            interface BoxElement {
                focus(): void;
                hide(): void;
                show(): void;
                setContent(content: string): void;
                pushLine(content: string): void;
                setLabel(label: string): void;
                setScrollPerc(perc: number): void;
                width?: number;
                height?: number;
                style: any;
                top?: number;
                left?: number;
                right?: number;
                bottom?: number;
            }

            interface FormElement extends BoxElement {
                submit(): void;
                destroy(): void;

            }

            interface TextElement extends BoxElement {}

            interface TextboxElement extends BoxElement {
                getValue(): string;
                setValue(value: string): void;
                key(key: string, callback: Function): void;
                readInput(callback: Function): void;
                focus(): void;
                clearValue(): void;
                inputOnFocus?: boolean;
            }
        }

        interface NodeScreenOptions {
            smartCSR?: boolean;
            title?: string;
        }

        interface Screen {
            title: string;
            append(element: Widgets.BoxElement | Widgets.ListElement): void;
            remove(element: Widgets.BoxElement | Widgets.ListElement): void;
            leave(): void;
            render(): void;
            key(keys: string[], callback: Function): void;
            focused: Widgets.BoxElement | Widgets.ListElement;
        }

        export function screen(options: NodeScreenOptions): Screen;
        export function box(options: Widgets.BoxOptions): Widgets.BoxElement;
        export function list(options: Widgets.ListOptions): Widgets.ListElement;
        export function text(options: Widgets.BoxOptions): Widgets.TextElement;
        export function form(options: Widgets.BoxOptions): Widgets.FormElement;
        export function textbox(options: Widgets.BoxOptions): Widgets.TextboxElement;
    }

    export = blessed;
} 