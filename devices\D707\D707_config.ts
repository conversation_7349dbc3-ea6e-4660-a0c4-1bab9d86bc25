export const DEVICE_NAME                    :string = 'BORK D707';
export const DEVICE_MAC                     :string = 'fb:15:10:24:0f:3a';                   // офисный массажер   
export const DEVICE_UUID                    :string = '51b7513274e7ed478cc6aa22c884056b';   // Ванин массажер    используется при работе с мак-ос
export const D707_CONTROL_SERVICE           :string = "a002";   // "0000a00200001000800000805f9b34fb";   !!!! массажер не адвертайзит ID сервисов      
export const CHAR_ID_TO_SUBSCRIBE_FOR_DATA  :string = "C305";    // 0000c305-0000-1000-8000-00805f9b34fb
export const CHAR_TO_WRITE_COMMANDS_TO      :string = "C304";        // 0000c304-0000-1000-8000-00805f9b34fb
export const D707_OTA_FIRMWARES_PATH        :string = "devices/D707/ota_firmwares"; // не прошивайте версию от пред-production устройств. работать не будет; восстановить моно только прошивкой проводом
export const D707_PROTOCOL_VERSION          :number = 2               // версия 1 - у пред-production устройств. этой утилитой не поддержвается