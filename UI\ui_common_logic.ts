// тут вспомогательные функции для окон - обработка нажатия кнопок, вызов попапов и т.п.

import * as blessed from 'blessed';
import { screen, menuBox, main_menu_list, submenu_list, logsBox, statusBox, selectPopupBox, helpText } from  './ui_elements';



type TreatKeysCallback = (focused: any, key: string) => void;

interface SelectPopupResult {
    content: string;
    index: number;
}



let keys_cb: TreatKeysCallback | null = null;
const status_bar_strings: string[] = [];



function init(main_menu_items: string[], submenu_items: string[], treat_keys_callback: TreatKeysCallback): void {
    keys_cb = treat_keys_callback;

    screen.append(menuBox);
    screen.append(logsBox);
    screen.append(selectPopupBox);
    screen.append(helpText);
    screen.append(statusBox);

    // set menu items
    main_menu_items.forEach(item => { main_menu_list.addItem(item); });
    submenu_items.forEach(item => { submenu_list.addItem(item); });
    screen.render();



    /****************************************
     *   screen keys handlers
     *************************************/
    screen.key(['escape', 'q'], async function (ch: string, key: any) {
        if (screen.focused === main_menu_list || screen.focused === logsBox) {
            if (keys_cb) keys_cb(screen.focused, 'escape');
        }

        if (screen.focused === submenu_list) {
            submenu_list.hide();
            main_menu_list.show();
            main_menu_list.focus();
            screen.render();
        }
    });

    screen.key(['tab'], function (ch: string, key: any) {
        const current_menu = main_menu_list.hidden ? submenu_list : main_menu_list;
        if (screen.focused === current_menu) {
            logsBox.focus();
            logsBox.style.border.fg = 'green';
            menuBox.style.border.fg = 'white';
        } else {
            current_menu.focus();
            logsBox.style.border.fg = 'white';
            menuBox.style.border.fg = 'green';
        }
        screen.render();
    });
}



/*************************************
 *   functions
 *************************************/
//    user input popup
function input_popup(window_name: string, default_value: string, expected_type: 'int' | 'float' | 'string'): Promise<number | string | null> {
    return new Promise((resolve, reject) => {
        const current_focus = screen.focused;
        // Create input form
        const form = blessed.form({
            parent: screen,
            top: 'center',
            left: 'center',
            width: '80%',
            height: 7,
            border: 'line',
            label: window_name,
            bg: 'blue',
            keys: true
        } as any);

        // Create text input
        const input = blessed.textbox({
            parent: form,
            top: 1,
            left: 2,
            right: 2,
            height: 1,
            value: default_value,
            inputOnFocus: true,
            style: {
                fg: 'white',
                bg: 'black'
            },
        } as any);

        // Create help text
        blessed.text({
            parent: form,
            bottom: 1,
            left: 'center',
            content: 'Enter: Submit, Esc: Cancel',
            style: {
                fg: 'white'
            }
        });

        // Handle input submission
        input.key('enter', function () {
            const value = (input as any).getValue();
            (form as any).destroy();
            (current_focus as any).focus();
            screen.render();

            let parsed_value: number;
            if (expected_type === 'int') {
                try {
                    parsed_value = parseInt(value);
                } catch (error) {
                    reject("value must be int");
                    return;
                }
                if (isNaN(parsed_value)) {
                    reject("no value entered");
                    return;
                }
                if (parsed_value < 0) {
                    reject("value must be positive");
                }
                resolve(parsed_value);

            } else if (expected_type === 'float') {
                try {
                    parsed_value = parseFloat(value);
                } catch (error) {
                    reject("value must be float");
                    return;
                }
                if (isNaN(parsed_value)) {
                    reject("no value entered");
                    return;
                }
                resolve(parsed_value);
            } else if (expected_type === 'string') {
                resolve(value);
            } else {
                reject("wrong data type as parameter");
            }
        });
        // Handle escape in input
        input.key('escape', function () {
            form.destroy();
            current_focus.focus();
            screen.render();
            resolve(null);
        });

        input.focus();
        screen.render();
    });
}



/**
 * Function to push text to the content window with optional colorization.
 * @param {string} text - The text to log.
 * @param {string} [color] - Optional color for the text (e.g., 'red', 'green', 'blue').
 */
function logContent(text: string, color: string = 'white'): void {
    let formattedText = `${new Date().toLocaleTimeString()}    `;
    if (color) {
        // Check if the color is a valid blessed color
        const validColors = ['red', 'green', 'yellow', 'blue', 'magenta', 'cyan', 'white'];
        if (validColors.includes(color.toLowerCase())) {
            formattedText += `{${color}-fg}${text}{/${color}-fg}`;
        } else {
            // If the color is not valid, log an error and use default formatting
            // console.error(`Invalid color specified: ${color}. Using default formatting.`);
            formattedText += text;
        }
    } else {
        formattedText += text;
    }

    logsBox.pushLine(formattedText);
    logsBox.setScrollPerc(100);
    screen.render();
}



async function clear_logs(): Promise<void> {
    logsBox.setContent('');
    screen.render();
}



// Function to update the status box text
function updateStatusBar(text_array: (string | null)[]): void {
    if (!Array.isArray(text_array)) {
        logContent("updateStatusBar: input must be an array");
        return;
    }

    if (statusBox.height < text_array.length) {
        menuBox.height = menuBox.height - (text_array.length - statusBox.height);
        logsBox.height = logsBox.height - (text_array.length - statusBox.height);
        helpText.bottom = helpText.bottom + (text_array.length - statusBox.height);

        statusBox.height = text_array.length;
    };

    for (let i = 0; i < text_array.length; i++) {
        if (text_array[i] != null) {
            status_bar_strings[i] = text_array[i] as string;
        }
    }
    statusBox.setContent(status_bar_strings.join('\n'));
    screen.render();
}



/**
 * Shows a popup with the given items and returns a promise that resolves 
 * with the selected item when the user makes a selection
 * 
 * @param {string[]} items - Array of items to display in the popup
 * @param {string} [title] - Optional title for the popup
 * @returns {Promise<{content: string, index: number}>} - The selected item and its index
 */
function show_select_popup(items: string[], title: string = ' Popup Menu '): Promise<SelectPopupResult | null> {
    if (!Array.isArray(items) || !items.every(item => typeof item === 'string')) {
        return Promise.reject(new Error("items must be an array of strings"));
    }
    if (items.length === 0) {
        return Promise.reject(new Error("items array must not be empty"));
    }

    return new Promise((resolve) => {
        // Update the popup items
        updatePopupItems(items, title);

        const selectHandler = (item: any, index: number) => {
            selectPopupBox.removeListener('select', selectHandler);
            selectPopupBox.hide();
            // menuBox.focus();  // focus will be handles from calling place
            screen.render();
            resolve({ content: item.content, index });
        };

        const escapeHandler = () => {
            selectPopupBox.removeListener('select', selectHandler);
            selectPopupBox.removeKey(['escape'], escapeHandler);

            selectPopupBox.hide();
            // menuBox.focus();    // focus will be handles from calling place
            screen.render();

            resolve(null);
        };

        selectPopupBox.on('select', selectHandler);
        selectPopupBox.key(['escape'], escapeHandler);

        selectPopupBox.show();
        selectPopupBox.focus();
        screen.render();
    });
}



/************************
 *    local functions
 ************************/
/**
 * Function to update popup items before showing the popup
 * @param {string[]} items - Array of items to display in the popup
 * @param {string} [title] - Optional title for the popup
 */
function updatePopupItems(items: string[], title: string = ' Popup Menu '): void {
    selectPopupBox.clearItems();
    items.forEach(item => selectPopupBox.addItem(item));
    selectPopupBox.setLabel(title);
    selectPopupBox.select(0);
    screen.render();
}



const ui_elements = {
    screen,
    menuBox,
    main_menu_list,
    submenu_list,
    logsBox,
    statusBox,
    selectPopupBox,
    helpText
};

export {
    init,
    input_popup,
    logContent,
    clear_logs,
    updateStatusBar,
    show_select_popup,
    ui_elements
}; 
