// Возвращает буфер с байтами команды - для отправки на устройство
// Описание протокола:   https://youtrack.bork.ru/articles/SH-A-175

import { Command, CommandParameter } from "../CommandTypes/command_types";



interface CommandsList {
    [key: string]: Command;
}

interface DeviceParameters {
    [key: string]: CommandParameter;
}



const commands_list: CommandsList = {
    SET_MODE: {                                      
        code: 0x06,
        params_qty: 2,
        p1: {name: "state", type: "enum", min: 0, max: 1, options: ['stopped','working']},
        p2: {name: "massage mode", type: "enum", min: 0, max: 4, options: ['none','active','awake','sooting', 'custom']},
    },

    UNLOCK: {
        code: 0x01,
        params_qty: 0
    },

    GET_DEVICE_ID: {
        code: 0xAB,
        params_qty: 0
    },

    SET_CUSTOM_MODE: {
        code: 0x20,
        params_qty: 0
    }
};

const device_parameters: DeviceParameters = {
    state: {
        type: 'enum',
        options: ['stopped','working','no_change'],
        name: 'state',
    },
    mode: {
        type: 'enum',
        options: ['none','active','awake','sooting', 'custom','no_change'],
        name: 'massage mode',
    },
    left_force: {
        type: 'enum',
        options: ['none','slow','medium','fast','no_change'], // none - to shift other values so their indexes match their code per protocol
        name: 'left force',
    },
    left_dir: {
        type: 'enum',
        options: ['reverse','forward','no_change'],
        name: 'left direction',
    },
    right_force: {
        type: 'enum',
        options: ['none','slow','medium','fast','no_change'],
        name: 'right force',
    },
    right_dir: {
        type: 'enum',
        options: ['reverse','forward','no_change'],
        name: 'right direction',
    },
    front_heat: {
        type: 'enum',
        options: ['low','medium','high','no_change'],
        name: 'front heat',
    },
    back_heat: {
        type: 'enum',
        options: ['low','medium','high','no_change'],
        name: 'back heat',
    },
    massage_spot: {
        type: 'enum',
        options: ['reciprocating', 'front_neck', 'back_neck','no_change'],
        name: 'massage spot',
    },
    recipr_mode: {
        type: 'enum',
        options: ['none','activating_network', 'soothing_mind', 'soothing_neck','no_change'],
        name: 'reciprocating mode',
    },
    time: {
        type: 'int',
        min: 0,
        max: 0xffff,
        name: 'time',
    },
    save: {
        type: 'enum',
        options: ['no', 'yes'],
        name: 'save',
    }
};

/**
 * Команда разблокровки управления по BLE и получения информации об устройстве
 * @param protocol_version - 1 для первой версии устройства(у нас были только прeд продажные образцы) и 2 для серийного
 * 
 */
function get_command_unlock(protocol_version: number): Buffer {
    return add_checksum(Buffer.from([0x80, protocol_version, 0x01, 0x00, 0x03, commands_list.UNLOCK.code, 0x01, 0x64, 0x00]));
}

/**
 * Устанавливает режим работы. (вк/выкл - state и режим - mode)
 * 
 * @param state - Custom_state
 * @param mode - Custom_mode
 * @param protocol_version -   1 для первой версии устройства(у нас были только перд продажные образцы) и 2 для серийного
 */
function get_command_set_mode(state: number, mode: number, protocol_version: number): Buffer {
    return add_checksum(Buffer.from([0x80, protocol_version, 0x01, 0x00, 0x03, commands_list.SET_MODE.code, state, mode, 0x00]));
}

/**
 *  Запрашивает id устройства (он же - мак адрес блютус адаптера)
 */
function get_command_device_id(protocol_version: number): Buffer {
    return add_checksum(Buffer.from([0x80, protocol_version, 0x01, 0x00, 0x02, commands_list.GET_DEVICE_ID.code, 0x02, 0x00]));
}

// parameters of enum type should be in string format
function get_command_custom_mode(
    protocol_version: number, 
    state: string, 
    mode: string, 
    l_force: string, 
    l_dir: string, 
    r_force: string, 
    r_dir: string, 
    f_heat: string, 
    b_heat: string, 
    spot: string, 
    recipr_mode: string, 
    time: number, 
    save: boolean
): Buffer | null {
    const res_data: number[] = [];
    res_data.push(0x80);
    res_data.push(protocol_version);
    res_data.push(0x01);
    res_data.push(0x00);
    res_data.push(0x0e);
    res_data.push(0x20);

    try {
        if (state === 'no_change') { res_data.push(0xff) } else { res_data.push(device_parameters.state.options?.indexOf(state) || 0)};
        if (state === 'no_change') { res_data.push(0xff) } else { res_data.push(device_parameters.mode.options?.indexOf(mode) || 0)};
        if (state === 'no_change') { res_data.push(0xff) } else { res_data.push(device_parameters.left_force.options?.indexOf(l_force) || 0)};
        if (state === 'no_change') { res_data.push(0xff) } else { res_data.push(device_parameters.left_dir.options?.indexOf(l_dir) || 0)};
        if (state === 'no_change') { res_data.push(0xff) } else { res_data.push(device_parameters.right_force.options?.indexOf(r_force) || 0)};
        if (state === 'no_change') { res_data.push(0xff) } else { res_data.push(device_parameters.right_dir.options?.indexOf(r_dir) || 0)};
        if (state === 'no_change') { res_data.push(0xff) } else { res_data.push(device_parameters.front_heat.options?.indexOf(f_heat) || 0)};
        if (state === 'no_change') { res_data.push(0xff) } else { res_data.push(device_parameters.back_heat.options?.indexOf(b_heat) || 0)};
        if (state === 'no_change') { res_data.push(0xff) } else { res_data.push(device_parameters.massage_spot.options?.indexOf(spot) || 0)};
        if (state === 'no_change') { res_data.push(0xff) } else { res_data.push(device_parameters.recipr_mode.options?.indexOf(recipr_mode) || 0)};
        res_data.push(time >>> 8);
        res_data.push(time % 0x100);
        res_data.push(save ? 1 : 0);
    } catch {
        return null;
    }

    res_data.push(0x00); // for check sum

    return add_checksum(Buffer.from(res_data));
}

// ******************** utils **********************
// добавляет контрольную сумму в последний байт массива
function add_checksum(buffer: Buffer): Buffer {
    if (buffer.length < 2) console.log("too small buffer");
    let sum = 0;
    for (let index = 0; index < buffer.length - 1; index++) {  
        sum += buffer[index];
    }

    sum = ~sum;
    sum = sum & 0xff;

    buffer[buffer.length - 1] = sum;
    return buffer;
}

export {
    device_parameters,
    commands_list,
    get_command_unlock,
    get_command_set_mode,
    get_command_device_id,
    get_command_custom_mode,
    add_checksum,            // write checksum to last byte of provided buffer. supposed to be empty
}; 