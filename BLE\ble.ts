import { EventEmitter } from 'node:events';
// import noble from '@abandonware/noble';
import noble from '@abandonware/noble/with-custom-binding';





// Types
type LoggingFunction = (message: string, color?: string) => void;
type SearchType = 'name' | 'mac' | 'uuid' | 'service' | 'manuf data' | null;



interface ManufDataProperty {
    data: string;
    position: number;
    lngth: number;
}

interface LookingFor {
    search_type: SearchType;
    property: string | ManufDataProperty | null;
}

interface Peripheral extends noble.Peripheral {
    advertisement: noble.Advertisement & {
        manufacturerData?: Buffer;
        serviceUuids?: string[];
        localName?: string;
    };
    rssi: number;
    address: string;
    uuid: string;
    id: string;
}



// Configuration
const DEVICE_NOT_FOUND_TIMEOUT: number = 5000; // ms
const BLE_COMMANDS_TIMEOUT: number = 15000; // ms
const IS_MACOS: boolean = process.platform === 'darwin'; // Определяем, запущено ли на MacOS

// Module variables
let perif: Peripheral | undefined = undefined;
let looking_for: LookingFor = { search_type: null, property: null };
let TX_char_set: noble.Characteristic | undefined;
let external_logging: LoggingFunction;
let found_devices: Peripheral[] = [];
let services_of_connected_device: noble.Service[] | null = null;
let subscribed_to_chars: string[] = [];    // Subscription to characteristics

// Event emitter
class BshEventsEmitter extends EventEmitter {}
const bshEmitter = new BshEventsEmitter();
bshEmitter.setMaxListeners(Infinity);



/************************************
 *     Ble functions    
 ***********************************/
async function init_ble(logging_function: LoggingFunction): Promise<void> {

    external_logging = logging_function;
    external_logging("Starting BLE...");
    // let noble2 = require('@abandonware/noble')

    if(noble._state === 'poweredOn')
    {
        external_logging('BLE poweredOn');
        bshEmitter.emit("ble ready");
    } else {
        noble.on('stateChange', async (state: string) => {
            external_logging('BLE '.concat(state));
            if (state === 'poweredOn') {
                bshEmitter.emit("ble ready");
            }
        });
    }


    noble.on('disconnect', () => {
        services_of_connected_device = null;
        subscribed_to_chars = [];
        perif = undefined;
        bshEmitter.emit("disconnected");
    });

    noble.on('discover', async (peripheral: Peripheral) => {
        external_logging(
            "    ".concat(
                !peripheral.advertisement.localName ? "         " : peripheral.advertisement.localName,
                "  ",
                peripheral.address,
                "  ",
                String(peripheral.rssi),
                " | id: ",
                peripheral.id ? peripheral.id : "null",
                " | uuid: ",
                peripheral.uuid ? peripheral.uuid : "null",
                "\n"
            )
        );

        let found_something: boolean = false;
        let formattedUuid: string = peripheral.uuid;
        if (IS_MACOS && formattedUuid && formattedUuid.length >= 32) {
            formattedUuid = `${formattedUuid.slice(0, 8)}-${formattedUuid.slice(8, 12)}-${formattedUuid.slice(12, 16)}-${formattedUuid.slice(16, 20)}-${formattedUuid.slice(20)}`;
        }

        switch (looking_for.search_type) {
            case 'manuf data':
                {
                if (peripheral.advertisement.manufacturerData === undefined) break;

                const prop = looking_for.property as ManufDataProperty;
                const m_d_slice = peripheral.advertisement.manufacturerData.toString('hex').toUpperCase().slice(
                    prop.position,
                    prop.position + prop.lngth
                );

                if (prop.data === m_d_slice) {
                    found_devices.push(peripheral);
                    found_something = true;
                }
                }
                break;
                
            case 'service':
                if (peripheral.advertisement.serviceUuids === undefined) break;

                if (peripheral.advertisement.serviceUuids.includes(looking_for.property as string)) {
                    found_devices.push(peripheral);
                    found_something = true;
                }
                break;

            case 'name':
                if (peripheral.advertisement.localName === undefined) break;

                if (peripheral.advertisement.localName === looking_for.property) {
                    found_devices.push(peripheral);
                    found_something = true;
                }
                break;

            case 'mac':
                if (peripheral.address === undefined) break;

                if (peripheral.address.toUpperCase() === looking_for.property) {
                    found_devices.push(peripheral);
                    found_something = true;

                    await noble.stopScanningAsync();
                    bshEmitter.emit("scan completed", found_devices);
                }
                break;

            case 'uuid':
                if (peripheral.uuid === undefined) break;

                if (peripheral.uuid === looking_for.property || formattedUuid === looking_for.property) {
                    found_devices.push(peripheral);
                    found_something = true;

                    await noble.stopScanningAsync();
                    bshEmitter.emit("scan completed", found_devices);
                }
                break;

            default:
                break;
        }

        if (found_something) {
            external_logging(
                "found the device: ".concat(
                    "name: ",
                    peripheral.advertisement.localName || "unknown",
                    "  mac: ",
                    peripheral.address,
                    "\n",
                    "                               manufacturer data: ",
                    peripheral.advertisement.manufacturerData ? peripheral.advertisement.manufacturerData.toString('hex') : "",
                    "\n",
                    "                               id: ",
                    peripheral.id ? peripheral.id : "null",
                    "\n",
                    "                               uuid: ",
                    peripheral.uuid ? peripheral.uuid : "null",
                    "\n",
                    "                               uuid formatted: ",
                    formattedUuid ? formattedUuid : "null",
                    "\n",
                    "                               address: ",
                    peripheral.address ? peripheral.address : "null"
                    ), 'green'
            );
        }
    });

    await waitForBle_ready();
}



async function find_all_by_name(name: string): Promise<Peripheral[] | null> {
    looking_for = { search_type: 'name', property: name };
    found_devices = [];

    await noble.startScanningAsync();
    const result = await waitForDevicesDiscovered();
    return result;
}



async function find_device_by_mac(device_mac: string): Promise<Peripheral | null> {
    looking_for = { search_type: 'mac', property: device_mac.toUpperCase() };
    found_devices = [];

    await noble.startScanningAsync();
    const result = await waitForDevicesDiscovered();
    return (result.length > 0) ? result[0] : null;
}



async function find_all_by_manuf_data(
    data: string,
    pos: number,
    length: number
): Promise<Peripheral[] | null> {
    found_devices = [];

    const looking_for_manuf_data = data.replace(new RegExp(':', 'g'), '').toUpperCase();
    looking_for = {
        search_type: 'manuf data',
        property: { data: looking_for_manuf_data, position: pos, lngth: length }
    };

    await noble.startScanningAsync();
    const result = await waitForDevicesDiscovered();
    return result;
}



async function find_device_by_uuid(device_uuid: string): Promise<Peripheral | null> {
    looking_for = { search_type: 'uuid', property: device_uuid };
    found_devices = [];

    await noble.startScanningAsync();
    const result = await waitForDevicesDiscovered();
    return result.length > 0 ? result[0] : null;
}



async function find_all_by_service(service_uuid: string): Promise<Peripheral[] | null> {
    looking_for = { search_type: 'service', property: service_uuid };
    found_devices = [];

    await noble.startScanningAsync();
    const result = await waitForDevicesDiscovered();
    return result;
}



async function connect(peripheral: Peripheral): Promise<noble.Service[] | null> {
    services_of_connected_device = null;
    // peripheral.on('onMtu', async (mtu: number) => {
    //     external_logging("======== MTU ======== changed ".concat(mtu.toString()));
    // });
    
    await peripheral.connectAsync();
    perif = peripheral;
    let services: noble.Service[] | null = null;
    
    try {
        services = await peripheral.discoverServicesAsync([]) as noble.Service[];
        for (const element of services) {
            if (element.uuid.length === 2) {
                external_logging(`fixed service uuid: ${element.uuid}`, 'yellow');
                element.uuid = '00'.concat(element.uuid); // костыль для косяка ble библиотеки, которая отбрасывает 00 в начале id сервиса.
            }

            let chars: noble.Characteristic[];
            try {
                chars = await element.discoverCharacteristicsAsync([]) as noble.Characteristic[];
            } catch (error) {
                external_logging("cant scan services: " + (error as Error).toString());
                continue;
            }
            
            if (!chars) {
                external_logging("cant scan services: no chars found", 'red');
                continue;
            }
            
            element.characteristics = chars;
            for (const ch of element.characteristics) {
                if ((ch as any)._serviceUuid.length === 2) {
                    (ch as any)._serviceUuid = '00'.concat((ch as any)._serviceUuid);
                }
                external_logging('found chars: '.concat(ch.uuid));
            }
        }
    } catch (error) {
        external_logging("cant scan services: " + (error as Error).toString());
    }

    if (services) {
        services_of_connected_device = services;
    } else {
        services_of_connected_device = null;
    }

    return services;
}



async function scan_all(time: number): Promise<void> {
    external_logging('Сканирование BLE устройств...');
    await noble.startScanningAsync();
    setTimeout(() => { stop_scanning(); }, time * 1000);
}



async function stop_scanning(): Promise<void> {
    await noble.stopScanningAsync();
    external_logging('stopped scanning');
}



async function disconnect(): Promise<void> {
    subscribed_to_chars = [];
    TX_char_set = undefined;
    services_of_connected_device = null;
    
    if (perif !== undefined) {
        services_of_connected_device = null;
        perif.once('disconnect', async () => {
            bshEmitter.emit("disconnected");
        });
        perif.disconnect();

        await waitForDisconnect();
        perif = undefined;
        external_logging("disconnected");
    } else {
        external_logging("cant disconnect: no device connected", 'yellow');
    }
}



async function discover_services_and_chars(): Promise<void> {
    if (get_connection_status() !== 'connected') 
    { 
        external_logging('cant scan for services: no device connected', 'yellow'); 
        return; 
    }

    const services = await perif.discoverServicesAsync([]) as noble.Service[];
    if (!services) {
        external_logging("cant scan services: no services found", 'red');
        return;
    }
    external_logging("");

    for (const element of services) {
        external_logging("service: ".concat(element.uuid));

        let chars: noble.Characteristic[];
        try {
            chars = await element.discoverCharacteristicsAsync([]) as noble.Characteristic[];
        } catch (error) {
            external_logging("cant scan services: " + (error as Error).toString());
            continue;
        }

        if (!chars) {
            external_logging("cant scan services: no chars found", 'red');
            continue;
        }

        for (const char of chars) {
            external_logging("        char: ".concat(char.uuid));
        }
    }
}



async function subscribe_to(char_uuid: string, data_callback: (data: Buffer, isNotification: boolean) => void): Promise<void> {
    if (get_connection_status() !== 'connected') {
        external_logging("cant subscribe: no device connected", 'yellow');
        return;
    }

    const char = await find_char_by_uuid(char_uuid);
    if (char === null) {
        external_logging("cant subscribe: char not found", 'red');
        return;
    }

    if (!subscribed_to_chars.includes(char_uuid)) {
        await char.subscribe();
        subscribed_to_chars.push(char_uuid);
    }
    char.removeAllListeners('data');
    char.on('data', data_callback);
    external_logging("subscribed to ".concat(char.uuid));
}



async function set_TX_char(char_uuid: string): Promise<void> {
    if (perif === undefined) {
        external_logging("cant subscribe: no device connected", 'red');
        return;
    }

    const char = await find_char_by_uuid(char_uuid);
    if (char === null) {
        external_logging("cant set TX char: id not found", 'yellow');
        return;
    }
    external_logging("set TX char: ".concat(char_uuid));

    TX_char_set = char;
}



function get_connection_status(): string {
    if (!perif) return 'disconnected';

    return perif.state;
}

/********************************
 *   Local Helper functions
 ********************************/
/**
 * Sends raw data to TX char (set by set_tx_chat)
 * Expects buffer as input
 */
async function send_data(data: Buffer, char_uuid?: string): Promise<string | void> {

    if (get_connection_status() === 'disconnected') {
        external_logging("no device connected");
        return;
    }

    external_logging(">> sending: ".concat(data.toString('hex')), 'magenta');

    if (char_uuid) {
        const targetChar = await find_char_by_uuid(char_uuid);
        if (targetChar) {
            await targetChar.writeAsync(data, false);
            return;
        }
        external_logging("internal err: specified char not found", 'red');
        return 'err';
    }

    if (TX_char_set === undefined) {
        external_logging("internal err: no TX char set", 'yellow');
        return 'err';
    }

    await TX_char_set.write(data, false);
}



async function read_data_from_char(char_uuid: string): Promise<Buffer | undefined> {
    const char = await find_char_by_uuid(char_uuid);

    if (char === null) {
        external_logging("cant read: char not found", 'red');
        return undefined;
    }

    const  data = await char.readAsync();
    return data;
}



async function sendToDevice_AndWaitForReply(buffer: Buffer): Promise<any> {
    const res = await send_data(buffer);
    if (res !== 'err') {
        return await waitForReply("raw_data", "-");
    } else {
        return 'err';
    }
}



async function waitForNextMessage(): Promise<any> {
    return waitForReply("raw_data", "-");
}



function waitForReply(reply: string, error: string): Promise<any> {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            reject('reply timeout');
        }, BLE_COMMANDS_TIMEOUT);

        bshEmitter.once(reply, (reply_data) => resolve(reply_data));
        bshEmitter.once(error, reject);
    });
}



function waitForBle_ready(): Promise<void> {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            reject('ble ready timeout');
        }, BLE_COMMANDS_TIMEOUT);
        bshEmitter.once("ble ready", () => resolve());
    });
}



function waitForDisconnect(): Promise<void> {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            reject('disconnect timeout');
        }, BLE_COMMANDS_TIMEOUT);
        bshEmitter.once("disconnected", () => resolve());
    });
}



function waitForDevicesDiscovered(): Promise<Peripheral[]> {
    if (
        looking_for.search_type === 'manuf data' ||
        looking_for.search_type === 'service' ||
        looking_for.search_type === 'name'
    ) {
        return new Promise((resolve) => {
            setTimeout(() => {
                stop_scanning();
                bshEmitter.emit("scan completed", found_devices);
            }, DEVICE_NOT_FOUND_TIMEOUT);

            bshEmitter.once("scan completed", (found_devices) => {
                resolve(found_devices);
            });
        });

    } else {
        return new Promise((resolve) => {
            setTimeout(() => {
                stop_scanning();
                found_devices = [];
                resolve(found_devices);
            }, DEVICE_NOT_FOUND_TIMEOUT);

            bshEmitter.once("scan completed", (found_devices) => resolve(found_devices));
        });
    }
}



async function find_char_by_uuid(char_uuid: string): Promise<noble.Characteristic | null> {
    if (services_of_connected_device === null) return null;

    for (const service of services_of_connected_device) {
        for (const char of service.characteristics) {
            if (char.uuid.toUpperCase() === char_uuid.toUpperCase()) {
                return char;
            }
        }
    }
    return null;
}



async function set_receive_char(char: string): Promise<void> {
    await subscribe_to(char, (data) => {
        bshEmitter.emit("raw_data", data);
    });
}



// ============== utils ================
function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}



function get_periph_with_better_signal_from_array(peripherals_array: Peripheral[]): Peripheral | null {
    if (!Array.isArray(peripherals_array) || peripherals_array.length === 0) {
        external_logging("Err:  empty periph array", 'yellow');
        return null;
    }

    try {
        let minRssi = peripherals_array[0].rssi;
        let bestPeripheral: Peripheral | null = null;

        for (const peripheral of peripherals_array) {
            if (peripheral.rssi >= minRssi) {
                minRssi = peripheral.rssi;
                bestPeripheral = peripheral;
            }
        }
        return bestPeripheral;
    } catch (error) {
        external_logging((error as Error).message, 'red');
        throw new Error("something is wrong with peripheral data");
    }
}



function calculate_crc16_modbus(data_bytes: Buffer): Buffer {
    let crc = 0;
    for (const byte of data_bytes) {
        crc ^= byte & 0xFF;
        for (let i = 0; i < 8; i++) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0x8408;
            } else {
                crc = crc >> 1;
            }
        }
    }
    return Buffer.from([(crc & 0xFF), (crc >> 8)]);
}



// Module exports
export default {
    main: {
        init_ble,                 // надо запустить перед использованием модуля.   acceptss logs callback
        
        scan_all,                 // сканировать эфир и логировать найденные устройства
        stop_scanning,            // остановить сканирование
        
        find_device_by_mac,       // ищет девайс по мак адресу. водвращает один! peripheral
        find_device_by_uuid,      // ищет девайс по UUID. водвращает один! peripheral ( работает только для MacOS)
        find_all_by_name,         // ищет девайсы по имени. возвращает массив! paripheral с данными ble advertizing и пр.
        find_all_by_manuf_data,   // ищет девайс по manufacturer data: ищет указанные данные в указанном месте и указанной длины
        find_all_by_service,      // ищет девай по C781 control service UUID (любую машину)
        
        connect,                  // принимает peripheral, полученный от find-функций выше, и коннектится
        disconnect,               // отключиться 
        
        subscribe_to,             // подписаться на изменение данных в характеристике
        discover_services_and_chars, // найти сервисы и характеристики у подключенного устройства

        set_TX_char,              // устанавливает в какую характеристику писать команду
        send_data,                // отправляет сырые данные как есть. optional second parameter - char to write to
        read_data_from_char,
        get_connection_status,    // получить текущий статус соединения   'error' | 'connecting' | 'connected' | 'disconnecting' | 'disconnected';
    },
    low_level: {                  // dont use these unless you know what you are doing
        set_receive_char,         // should be set befores using ..wait.. functions below. accepts 4 butes uuid as string
        sendToDevice_AndWaitForReply,
        waitForNextMessage,
        find_char_by_uuid
    },
    utils: {
        delay,
        calculate_crc16_modbus,
        get_periph_with_better_signal_from_array
    },
    
}; 

export  { Peripheral }