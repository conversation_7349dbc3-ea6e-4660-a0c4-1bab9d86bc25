/**
 *     Формат команд  ОТА
 * 
 *   Старт ОТА
 *     80 02 01 00 09  [6D 01 56  44 30 32 5F 42 FF]  69       .....m.VD02_B.i
 *     80 - device  02 - protocol V  01 - Колво-команд  00 - номер текущей команды  09 - длина данных команды    ...команда+данные...  69 чексумма
 *     команда+данные:
 *       [6D 01 56  44 30 32 5F 42 FF] 
 *       6D - старт ОТА
 *       01 - entry mode.. не ясно что это но всегда = 1
 *       56 44 30 32 5F 42 - версия строкой   типа   .VD02_B.   
 *       FF - тоже не ясно но всегда FF
 * 
 *   ответ 80 02 01 00 02 6D 01 0C    всегда один и тот же
 * 
 * 
 *
 *   Пакеты с данными
 *   80 02 00 00 C9 6E ........ данные и в конце чек-сумма ( по тома же алгоритму что для все других бле пакетов. НЕ crc16)                         
 *    С9 - длина данных
 *    6E - команда  пакета с данными
 *   00 00 перед С9 - тут номер пакета с данными , в big endian.  т.е. второй пакет будет с номером 00 01 и т.д.
 * 
 *  ответ:   80 02 01 00 04 6E 01 00  01 08  где 00 01 перед 08 - номер полученного ппакета + 1 (номер следующего?)
 * 
 * 
 * 
 *   Завершение ОТА
 *  80 02 01 00 03 6F 74 1D  79
 *  03 длина команды+данных
 *  6F команды
 *  74 1D  crc16
 *  79 контрольная сумма пакета
 * 
 *  ответ:    80 02 01 00 02 6F 01 0A   похоже всегда один и тот же. 01 перед 0A означает успех прошивки. 0 - провал
 */

import * as fs from 'fs';
import * as crc from 'crc';

import bleModule from '../../BLE/ble';
import * as D707_commands from './D707_commands';
import { CHAR_ID_TO_SUBSCRIBE_FOR_DATA } from './D707_config';

type LogsCallback = (text: string) => void;

const ota_start_command: Buffer = Buffer.from([0x80, 0x02, 0x01, 0x00, 0x09, 0x6D, 0x01, 0x56, 0x44, 0x30, 0x32, 0x5F, 0x42, 0xFF, 0xff]); // в последний байт надо вписать сумму пакета
const ota_data_packet_header: Buffer = Buffer.from([0x80, 0x02, 0x00, 0x00, 0xC9, 0x6E]);
const ota_done_command: Buffer = Buffer.from([0x80, 0x02, 0x01, 0x00, 0x03, 0x6F, 0xcc, 0xcc, 0xff]);  // в последние три байта надо вписать crc прошивки и сумму пакета

let firmware_file: Buffer;
let send_logs: LogsCallback;

async function start_OTA(file: string, logs_cb: LogsCallback): Promise<void> {
    send_logs = logs_cb;
    
    // Чтение файла прошивки в буфер
    try {
        const data = await fs.promises.readFile(file);
        firmware_file = Buffer.from(data.buffer as ArrayBuffer);
        logs_cb('Файл прошивки считан в буфер. размер: '.concat(firmware_file.length.toString(), "байт"));
    } catch (err) {
        throw new Error(err as any);
    }

    let reply: any;

    await bleModule.low_level.set_receive_char(CHAR_ID_TO_SUBSCRIBE_FOR_DATA);

    // старт ОТА
    logs_cb("Starting OTA");
    D707_commands.add_checksum(ota_start_command);
    reply = await bleModule.low_level.sendToDevice_AndWaitForReply(ota_start_command);
    if (reply === 'err') return;
    if(reply[5] != 0x6D) reply = await bleModule.low_level.waitForNextMessage();
    if(reply[5] != 0x6D) reply = await bleModule.low_level.waitForNextMessage();
    if(reply[5] != 0x6D) {
        logs_cb("неверный ответ на пакет старта ОТА !!!: "); 
        return;
    }

    // пересылка пакетов
    let next_piece_number = 0;
    let next_chunk_to_send: Buffer | undefined;
    let buffer_to_send: Buffer;
    const total_pieces = firmware_file.length/200;
    
    do {
        // logs_cb("sending piece N", next_piece_number);
        next_chunk_to_send = getBufferPart(firmware_file, next_piece_number);
        if(next_chunk_to_send != undefined) {
            buffer_to_send = Buffer.concat([ota_data_packet_header, next_chunk_to_send, Buffer.from([0x00])]);
            // номер пакета
            buffer_to_send[2] = next_piece_number >> 8;
            buffer_to_send[3] = next_piece_number % 0x100;
            // длина пакета
            buffer_to_send[4] = next_chunk_to_send.length + 1;  // +1 это байт с кодом команды
            // чек сумма
            D707_commands.add_checksum(buffer_to_send);

            reply = await bleModule.low_level.sendToDevice_AndWaitForReply(buffer_to_send);
            if(reply[5] != 0x6E) reply = await bleModule.low_level.waitForNextMessage();
            if(reply[5] != 0x6E) reply = await bleModule.low_level.waitForNextMessage();
            if(reply[5] != 0x6E) {
                logs_cb("неверный ответ на пакет с данными !!!");
                return;
            }
            
            if(reply[7]* 0x100 + reply[8] !== next_piece_number + 1) {
                reply = await bleModule.low_level.waitForNextMessage();
                if(reply[7]* 0x100 + reply[8] !== next_piece_number + 1) logs_cb("пакет не записан. повторная отправка");
                else next_piece_number++;
            } else { 
                next_piece_number++;
            }
            await delay(200);   // без паузы устройство пропускает каждый второй пакет. слишком быстро.   можно уменьшить паузу и посмотреть посыпятся ли повторные отправки. 
            logs_cb('progress:'.concat((next_piece_number/total_pieces * 100).toString(), "%"));
        }
    } while (next_chunk_to_send != undefined);
    
    // завершение ОТА
    ota_done_command[6] = get_crc16(firmware_file) >> 8;
    ota_done_command[7] = get_crc16(firmware_file) % 0x100;
    D707_commands.add_checksum(ota_done_command);
    reply = await bleModule.low_level.sendToDevice_AndWaitForReply(ota_done_command);
    if(reply[5] != 0x6F) reply = await bleModule.low_level.waitForNextMessage();
    if(reply[5] != 0x6F) reply = await bleModule.low_level.waitForNextMessage();
    if(reply[5] != 0x6F) { 
        logs_cb("wrong reply to ota done !!!!!!!");
        return;
    }

    if(reply[6] === 0) logs_cb("OTA FAILED !!!");    
    if(reply[6] === 1) logs_cb("OTA COMPLETED !    device should reboot");    
}

// Функция для получения части буфера
function getBufferPart(buffer: Buffer, partNumber: number, partSize: number = 200): Buffer | undefined {
    const start = partNumber * partSize;
    const end = start + partSize;
    
    // Если стартовый индекс выходит за границы буфера, вернуть undefined
    if (start >= buffer.length) {
        send_logs("reached end of file");
        return undefined;
    }

    // Получить часть буфера
    const result = Buffer.from(buffer.slice(start, Math.min(end, buffer.length)));
    return result;
}

// Функция вычисления CRC16 всего файла. почему то использован вариант алгоритма для modbus
function get_crc16(buffer: Buffer): number {
    const crc16_for_modbus = crc.crc16modbus(buffer);
    send_logs("crc: ".concat(crc16_for_modbus.toString(16)));
    return crc16_for_modbus;
}

function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

export {
    start_OTA
}; 