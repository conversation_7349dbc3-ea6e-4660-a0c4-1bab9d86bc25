/*
 *   команды формируются в виде:
 *   58     01     01     B10D     0C000000     01        [доп. data нет]        CRC16(2 байта)
 *   |      |      |      |        |             |
 *   head   devT   fncT   cmdNum   length(4B)    cmdCode1
 *  
 */
import ble from '../../BLE/ble';

import { Command, CommandParameter } from '../CommandTypes/command_types';



type CommandsList = {
    [key: string]: Command;
};



const commands_list: CommandsList = {
    IN_TO_GRINDER_MODE_AND_SET_SIZE_AND_SPEED: {
        code: 8006,
        params_qty: 2,
        p1: {name: "grind size", type: "int", min: 0, max: 100},
        p2: {name: "grind speed", type: "int", min: 0, max: 100}
    },
    START_GRINDING: {
        code: 3500,
        params_qty: 3,
        p1: {name: "grinder adjustment speed", type: "int", min: 1000, max: 2000},
        p2: {name: "grind size", type: "int", min: 0, max: 100},
        p3: {name: "grind speed", type: "int", min: 0, max: 100}
    },
    STOP_GRINDING: {
        code: 3505,
        params_qty: 0
    },
    CALIBRATE_GRINDER: {          // в табличке как будто бы упомянут параметр на работает и без него
        code: 3502,
        params_qty: 0
    },
    GRINDER_QUIT: {
        code: 8012,
        params_qty: 0
    },
    // CUP SCREW
    CUP_RIGHT: {
        code: 2501,
        params_qty: 1,
        p1: {name: "pulses_freq", type: "int", min: 1, max: 5000}
    },
    CUP_LEFT: {
        code: 2500,
        params_qty: 1,
        p1: {name: "pulses_freq", type: "int", min: 1, max: 5000}
    },
    CUP_SHAKE: {
        code: 2502,
        params_qty: 0,
    },
    CUP_STEP_RIGHT: {
        code: 2504,
        params_qty: 0,
    },
    CUP_STEP_LEFT: {
        code: 2503,
        params_qty: 0,
    },    
    CUP_STOP: {
        code: 2505,
        params_qty: 0,
    },    
    // BREWER
    BREWER_IN: {                                    // V     работает, но не переходит напрямую из гриндера, надо из главноого экрана
        code: 8007,                                   // можно слать без параметров, и сохранить текущие настройки
        params_qty: 2, 
        p1: {name: "rotation", type: "enum", min: 1, max: 3, options: ['center','circle','spiral']},
        p2: {name: "temperature", type: "float", min: 0, max: 2000}    // значение температуры умножается на 10 и преобразуется во float 
    },
    BREWER_START: {
        code: 4506,
        params_qty: 5,
        p1: {name: "flow rate", type: "float", min: 0, max: 3},  //   FLOAT   диапазон надо проверить
        p2: {name: "water volume", type: "float",min: 0, max: 1000000000},  //   FLOAT   диапазон надо проверить
        p3: {name: "temperature", type: "float",min: 0, max: 2000},   // в градусах или фаренгейтах * 10  есть room temp. как задать в доках нету  диапазон надо проверить
        p4: {name: "rotation", type: "enum",min: 1, max: 3, options: ['center','circle','spiral']},     // вращение насадки с кипятком
        p5: {name: "mode?", type: "float", min: 0, max: 10000000}         // надо проверить
    },
    BREWER_PAUSE: {                                                     //  V  работает
        code: 8019, 
        params_qty: 0,
    },
    BREWER_RESTART: {                                                    //  V  работает
        code: 8021,
        params_qty: 0,
    },
    BREWER_STOP: {
        code: 4507,
        params_qty: 0,
    },
    BREWER_QUIT: {
        code: 8013,
        params_qty: 0,
    },
    BREWER_WATER_SOURCE: {
        code: 4508,
        params_qty: 1,
        p1: {name: "water source", type: "enum", min: 0, max: 1, options: ['tank','tap']},
    },
    BREWER_SET_TEMPERATURE: {                                            // V     работает. значение надо умножить на 10 перед отправкой. и тут почему то INT
        code: 4510,
        params_qty: 1,
        p1: {name: "temperature", type: "int", min: 0, max: 2000},  // в градусах или фаренгейтах 
    },
    BREWER_SET_PATTERN: {
        code: 8016,
        params_qty: 1,
        p1: {name: "rotation", type: "enum", min: 0, max: 3, options: ['center','circle','spiral']},    // 0 = center, 1 = circle , 2 = спираль
    },
    // SCALE
    SCALE_ENTER: {                                                  
        code: 8003,
        params_qty: 0,
    },
    SCALE_EXIT: {                                                   //  V    работает, в т.ч. выходит из режима помола и гриндера и установки единиц веса
        code: 8014,
        params_qty: 0,
    },
    SCALE_RESET: {                                                  //   V   работает, переходит в режим весов из главного экрана, но не из грндера и не из брювера
        code: 8500,
        params_qty: 0,
    },
    SCALE_CALIBRATE: {                                                  //   V   работает, переходит в режим весов из главного экрана, но не из грндера и не из брювера
        code: 50038,
        params_qty: 0,
    },
    
    // RECIPES
    RECIPES_SET_BYPASSING: {                                                  //   V   работает, переходит в режим весов из главного экрана, но не из грндера и не из брювера
        code: 8102,
        params_qty: 0,
    },
    RECIPES_SET_CUP: {                                                  //   V   работает, переходит в режим весов из главного экрана, но не из грндера и не из брювера
        code: 8104,
        params_qty: 0,
    },
    RECIPES_R_SEND: {                                                  //   V   работает, переходит в режим весов из главного экрана, но не из грндера и не из брювера
        code: 8001,
        params_qty: 0,
    },
    RECIPES_R_SEND_NO_GRIND: {                                                  //   V   работает, переходит в режим весов из главного экрана, но не из грндера и не из брювера
        code: 8004,
        params_qty: 0,
    },
    RECIPES_R_EXECUTE: {                                                  //   V   работает, переходит в режим весов из главного экрана, но не из грндера и не из брювера
        code: 8002,
        params_qty: 0,
    },
    
    // AUTOMODE SEND RECIPE
    RD_EASYMODE_RECIPE_SEND: {
        code: 11510,
        params_qty: 8,
        p1: {name: "slot N", type: "int", min: 0, max: 2},
        p2: {name: "bypass", type: "int", min: 0, max: 1},
        p3: {name: "cup type", type: "enum", min: 0, max: 12, options: ['Xpod', 'Xdripper', 'Other', 'Tea']},
        p4: {name: "scale on off", type: "int", min: 0, max: 1},
        p5: {name: "tea pour qty", type: "int", min: 0, max: 7},
        p6: {name: "recipe hex", type: "byte array", min: 5, max: 100},
        p7: {name: "grind size", type: "int", min: 0, max: 100},
        p8: {name: "ratio", type: "int", min: 0, max: 1000000000},
    },

    // UTILS
    UTILS_SET_WEIGHT_UNITS: {                                        //    переходит на экран установки единиц.  НЕ в весы
        code: 8005,
        params_qty: 1,
        p1: {name: "weight units", type: "enum", min: 0, max: 2, options: ['grams','ml','oz']},
    },
    UTILS_SET_BRIGHTNESS: {
        code: 8103,
        params_qty: 1,
        p1: {name: "brightness", type: "int", min: 0, max: 16},
    },
    UTILS_SET_TEMPERATURE_UNITS: {
        code: 8010,
        params_qty: 1,
        p1: {name: "temperature units", type: "enum", min: 0, max: 1, options: ['F','C']},  
    },
    UTILS_SET_MODE: {                                               // 9132 7856 = easy   0 = pro   !!!  другой хедер !!!   580102  
        code: 11511,                                                // работает
        params_qty: 1,
        p1: {name: "mode", type: "enum", min: 0, max: 1, options: ['auto','pro']},
        menu_item: true                                             // !!!!!   results in different header" 580102    
    },
    NOTIFY_MCU_MTU_NEGOTIATION: {
        code: 8100,
        params_qty: 0,
    },
    
    // replies to recipe commands
    REPLY_TO_SET_BYPASS: {
        code: 8102,
        params_qty: 0,
    },
    REPLY_TO_SET_CUP_TYPE: {
        code: 8104,
        params_qty: 0,
    },
    REPLY_TO_SET_RECIPE: {
        code: 8001,
        params_qty: 0,
    },
    REPLY_TO_EXEC_RECIPE: {
        code: 8002,
        params_qty: 0,
    },

    RD_BREWER_TEMPERATURE: {
        code: 8108,
        params_qty: 1,
        p1: {name: "temperature", type: "int", min: 0, max: 2000},
    },

    RD_UNIT_CHANGE: {
        code: 8015,
        params_qty: 3,
        p1: {name: "weight units", type: "enum", min: 0, max: 2, options:['grams','ml','oz']},  
        p2: {name: "temperature units", type: "enum", min: 0, max: 1, options:['F','C']},
        p3: {name: "water inlet mode", type: "enum", min: 0, max: 2, options:['tank','tap']}
    },
    RD_BackToHome: {
        code: 8022,
        params_qty: 0,
    },
    RD_CURRENT_WEIGHT: {
        code: 10507,
        params_qty: 1,
        p1: {name: "weight", type: "int", min: 0, max: 2000},
    },
    RD_Brewer_Stop: {
        code: 40511,
        params_qty: 0,
    },
    RD_MachineInfo: {
        code: 40521,
        params_qty: 0,
    }, 
    RD_WEIGHT_DATA: {                                               // машина все время шлет данные о весе
        code: 40523, 
        params_qty: 1,
        p1: {name: "grind size", type: "float", min: 0, max: 2000}      // max ?
    }, 
    RD_GearReport: {                                               // машина все время шлет данные о весе
        code: 40505, 
        params_qty: 1,
        p1: {name: "grinder calibration progress", type: "int", min: 0, max: 10000000}      // max ?
    }, 
    RD_CurrentGrinder: {                                               // машина все время шлет данные о весе
        code: 40526, 
        params_qty: 1,
        p1: {name: "grinder calibration done", type: "int", min: 0, max: 10000000}      // max ?
    }, 
    RD_POURED_WATER_DATA: {
        code: 20501,
        params_qty: 1,
        p1: {name: "water volume", type: "float", min: 0, max: 10000000}   // max ?
    },
    
    RD_MachineSleeping: {       // шлет когда заснула
        code: 8009,
        params_qty: 0,
    },
    RD_MachineNotSleeping: {    // шлет когда проснулась
        code: 8011,
        params_qty: 0,
    },
    RD_BREWER_COFFEE_START: {         // ???????
        code: 40502,
        params_qty: 2,
        p1: {name: "grind size", type: "int", min: 0, max: 10000000},
        p2: {name: "ratio", type: "int", min: 0, max: 10000000}
    },
    RD_Grinder_Stop: {         // ???????
        code: 40507,
        params_qty: 0,
    },
    RD_Pods: {        
        code: 40501,
        params_qty: 0,
    },
    RD_BLOOM: {        
        code: 40510,
        params_qty: 0,
    },
    RD_ENJOY: {        
        code: 40512,
        params_qty: 0,
    },
    RD_ENJOY2: {        
        code: 40513,
        params_qty: 0,
    },
    RD_BYPASS: {        
        code: 40520,
        params_qty: 0,
    },
    WATER_TANK_WATER_LEVEL_LOW: {   // мало воды в баке
        code: 40522,
        params_qty: 0,
    },
    RD_GRINDER_SIZE: {
        code: 8105,
        params_qty: 0,
    },
    RD_GRINDER_SPEED: {
        code: 8106,
        params_qty: 0,
    },
    RD_BREWER_MODE: {
        code: 8107,
        params_qty: 0,
    },
    RD_IN_GRINDER: {
        code: 9000,
        params_qty: 2,
        p1: {name: "grind size", type: "int", min: 0, max: 100000000},
        p2: {name: "grind speed", type: "int", min: 0, max: 10000000},
    },
    RD_IN_BREWER: {
        code: 9001,
        params_qty: 3,
        p1: {name: "water volume", type: "int", min: 0, max: 100000000},
        p2: {name: "temperature", type: "int", min: 0, max: 10000000},
        p3: {name: "mode", type: "int", min: 0, max: 3},
    },
    RD_exit_scale: {
        code: 9002,
        params_qty: 0,
    },
    RD_GRINDER_BEGIN: {
        code: 9003,
        params_qty: 0,
    },
    RD_OUT_GRINDER: {
        code: 9004,
        params_qty: 0,
    },
    RD_BREWER_BEGIN: {
        code: 9005,
        params_qty: 0,
    },
    RD_OUT_BREWER: {
        code: 9006,
        params_qty: 0,
    },
    RD_WEIGHING_RESET_TO_ZERO: {
        code: 9007,
        params_qty: 0,
    },
    RD_OUT_SCALE: {
        code: 9008,
        params_qty: 0,
    },
    RD_GRINDER_PAUSE: {
        code: 9009,
        params_qty: 0,
    },
    RD_BREWER_PAUSE: {
        code: 9010,
        params_qty: 0,
    },
    RD_TEA_RECIP_RESTART: {
        code: 9011,
        params_qty: 0,
    },
    RD_TEA_RECIP_SOAK: {
        code: 9012,
        params_qty: 0,
    },
    RD_EASYMODE_BEGIN: {
        code: 8111,
        params_qty: 0,
    },
    RD_TEA_RECIP_CHANGE_SOAK_TIME: {
        code: 8113,
        params_qty: 1,   // soak time
    },
    READ_ROTATION_RADIUS: {
        code: 11506,
        params_qty: 1,
        p1: {name: "radius", type: "int", min: 680, max: 1000}
    },
    READ_VIBRATION_PPS : {
        code: 11508,
        params_qty: 1,
        p1: {name: "vibration pps", type: "int", min: 1000, max: 1500}
    },
    RD_EASYMODE_RECIPE_STATE: {
        code: 11518,
        params_qty: 0,
    },

    // ????
    U_PAGE_NUMBER: {
        code: 8023,
        params_qty: 1,
        p1: {name: "pageNumber", type: "int", min: 0, max: 0},
    },
    WRONG_CRC: {                             // вроде присылает этот код если неправильный CRC
        code: 65534,
        params_qty: 0,
    },

    // status codes , page 0 for all
    STATUS_GRINDING: {
        code: 0,
        params_qty: 0,
    },
    STATUS_GRINDING_COMPLETED: {
        code: 2,
        params_qty: 0,
    },
    STATUS_BREWING: {
        code: 8,
        params_qty: 0,
    },
    STATUS_BREWING_COMPLETED: {
        code: 10,
        params_qty: 0,
    },
    STATUS_PAUSE_DURING_BREWING: {
        code: 9,
        params_qty: 0,
    },
};



/**
 * @param command    command, from commands list
 * @param params    parameters values, to be put into final raw command
 * @returns 
 */
function make_command(command: Command, ...params: any[]): Buffer {
  
    if(command == commands_list.RD_EASYMODE_RECIPE_SEND) return make_auto_mode_recipe_command(...params);

    if (params.length !== command.params_qty)
    {
        throw new Error(`wrong number of params: should be ${command.params_qty}. provided: ${params}`);
    }

    for (let i = 0; i < params.length; i++) 
    {
        const param = params[i];
        const param_name = `p${i + 1}` as keyof Command;
        
        const currentParam = command[param_name] as CommandParameter;
        
        if (currentParam && isCommandParam(currentParam) && (param < currentParam.min || param > currentParam.max)) {

            throw new Error(`param ${param_name} out of range: ${currentParam.min} - ${currentParam.max}, value : ${param}`);  
        }        
    }


    const HEAD = "58";
    const DEVICE_TYPE = "01";
    let FUNCTION_TYPE
    if (command.menu_item) {
        FUNCTION_TYPE = "02";
    } else {
        FUNCTION_TYPE = "01";
    }
    const COMMAND_CODE = "01";
    let command_number = command.code.toString(16).padStart(4, '0').toUpperCase();
    command_number = command_number.slice(2, 4) + command_number.slice(0, 2);

    let data_length = 12;
    let data_string = "";

    for (let i = 0; i < params.length; i++) {
        let param = params[i];
        data_length += 4;        
        let param_hex = "";

        const param_name = `p${i + 1}`as keyof Command; 
        const currentParam = command[param_name] as CommandParameter;

        if (currentParam && isCommandParam(currentParam) && (currentParam.type === 'int' || currentParam.type === 'enum'))
        {
            if(command.code === commands_list.BREWER_SET_TEMPERATURE.code ) {
                param = param * 10;
            }
            if (command.code === commands_list.UTILS_SET_MODE.code && param === 1)
            {
                data_string += "91327856";
            } else {
                param_hex = param.toString(16).padStart(4, '0').toUpperCase();
                param_hex = param_hex.slice(2, 4) + param_hex.slice(0, 2);
                data_string += param_hex + "0000";
            }
        } else if (currentParam && isCommandParam(currentParam) && currentParam.type === 'float') {
            if(command.code === commands_list.BREWER_IN.code ) {
                param = param * 10;
            }

            const floatBuffer = Buffer.alloc(4);
            floatBuffer.writeFloatLE(param, 0);
            data_string += floatBuffer.toString('hex').toUpperCase();
        } else {
            throw new Error("unsupported param type");
        }
    }

    let length_hex = data_length.toString(16).padStart(4, '0').toUpperCase();
    length_hex = length_hex.slice(2, 4) + length_hex.slice(0, 2);

    const result = HEAD + DEVICE_TYPE + FUNCTION_TYPE + command_number + length_hex + "0000" + COMMAND_CODE + data_string;

    const result_buffer = Buffer.from(result, 'hex');
    const crc = ble.utils.calculate_crc16_modbus(result_buffer);
    const crc_buffer = crc.toString('hex').padStart(4, '0').toUpperCase();

    return Buffer.concat([result_buffer, Buffer.from(crc_buffer, 'hex')]);
}



// Note: The original command definition has params_qty: 3, but lists 8 parameters (p1-p8).
// Assuming all 8 parameters are expected for this specific command based on the detailed fields.
function make_auto_mode_recipe_command(...params: any[]): Buffer {
    const expectedParamCount = 8; // p1 through p8
    
    if (params.length != expectedParamCount) {
        throw new Error(`Auto mode recipe command RD_EASYMODE_RECIPE_SEND requires 8 parameters, but ${params.length} were provided`);
    }

    const [slotN, bypass, cupType, scaleMode, teaPourQty, recipeBytes, grindSize, ratio] = params;

    // Validate parameters
    const cmd = commands_list.RD_EASYMODE_RECIPE_SEND;

    if (!cmd.p1 || !cmd.p2 || !cmd.p3 || !cmd.p4 || !cmd.p5 || !cmd.p6 || !cmd.p7 || !cmd.p8) {
        throw new Error(`Internal error: Validation rule missing for parameter`);
    }

    if (slotN < cmd.p1.min || slotN > cmd.p1.max) {
        throw new Error(`Slot number must be between 0 and 2, but got ${slotN}`);
    }

    if (bypass < cmd.p2.min || bypass > cmd.p2.max) {
        throw new Error(`Bypass must be 0 or 1, but got ${bypass}`);
    }

    if (cupType < cmd.p3.min || cupType > cmd.p3.max) {
        throw new Error(`Cup type must be between 0 and 12, but got ${cupType}`);
    }

    if (scaleMode < cmd.p4.min || scaleMode > cmd.p4.max) {
        throw new Error(`Scale mode must be 0 or 1, but got ${scaleMode}`);
    }

    if (teaPourQty < cmd.p5.min || teaPourQty > cmd.p5.max) {
        throw new Error(`Tea pour quantity must be between 0 and 7, but got ${teaPourQty}`);
    }

    if (!Buffer.isBuffer(recipeBytes)) {
        throw new Error(`Recipe bytes must be a Buffer, but got ${typeof recipeBytes}`);
    }

    if (grindSize < cmd.p7.min || grindSize > cmd.p7.max) {
        throw new Error(`Grind size must be between 0 and 100, but got ${grindSize}`);
    }


    // --- Command Generation Logic ---
    const command = commands_list.RD_EASYMODE_RECIPE_SEND;
    const HEAD = "58";
    const DEVICE_TYPE = "01";
    // RD_EASYMODE_RECIPE_SEND does not have menu_item: true
    const FUNCTION_TYPE = "02";
    const COMMAND_CODE = "01"; // Assuming this is standard based on make_command
    
    // Convert command code to little-endian hex string
    let command_number = command.code.toString(16).padStart(4, '0').toUpperCase();
    command_number = command_number.slice(2, 4) + command_number.slice(0, 2);
    
    const data_length = 3 + 2 + 4 + 1 + 2 + params[5].length + 2 + 2; // Base length: HEAD(3)+CMD_NUM(2)+LEN(4)+CMD_CODE(1) + slot&sett(2) + hex arr + grin&flow(2) + crc
    let data_string = "";
    
    data_string += params[0].toString(16).padStart(2, '0').toUpperCase();
    data_string += generateRecipeSettingsByte(params[1], params[2], params[3], params[4]).toString(16).padStart(2, '0').toUpperCase();
        
    data_string += params[5].toString('hex').toUpperCase();
    data_string += params[6].toString(16).padStart(2, '0').toUpperCase();
    data_string += (params[7]*10).toString(16).padStart(2, '0').toUpperCase();
    
    // Convert total length to little-endian hex string
    let length_hex = data_length.toString(16).padStart(4, '0').toUpperCase();
    length_hex = length_hex.slice(2, 4) + length_hex.slice(0, 2);
    length_hex += "0000";
    
    // Construct the full command hex string before CRC
    const result_hex = HEAD + DEVICE_TYPE + FUNCTION_TYPE + command_number + length_hex + COMMAND_CODE + data_string;
    
    // Convert to buffer and calculate CRC
    const result_buffer = Buffer.from(result_hex, 'hex');
    const crc = ble.utils.calculate_crc16_modbus(result_buffer);
    const crc_buffer = crc.toString('hex').padStart(4, '0').toUpperCase();
    
    // Return the final command buffer including CRC
    return Buffer.concat([result_buffer, Buffer.from(crc_buffer, 'hex')]);
}



/**
 * Generates a single byte bitmask from recipe settings.
 * Bit allocation:
 * - bypass (1 bit):     Position 7
 * - cup_type (3 bits):  Positions 6-4
 * - scale_mode (1 bit): Position 3
 * - tea_pour_N (3 bits): Positions 2-0
 *
 * @param {number} bypass - 0 or 1
 * @param {string} cup_type - 0, 2, 4, or 12 (Note: 12 requires 4 bits, will be masked to 3 bits (value 4))
 * @param {number} scale_mode - 0 or 1
 * @param {number} tea_pour_N - 0 to 7
 * @returns {number | null} A single byte (0-255) representing the combined settings.
 */
function generateRecipeSettingsByte(bypass: number, cup_type: string, scale_mode: number, tea_pour_N: number): number {
    // Validate inputs
    if (bypass < 0 || bypass > 1) {
        throw new Error(`Bypass must be 0 or 1, but got ${bypass}`);
    }
    
    const validCupTypes = ['Xpod', 'Xdripper', 'Other', 'Tea'];
    if (!validCupTypes.includes(cup_type)) {
         throw new Error(`Invalid cup_type value: ${cup_type}. Must be one of ${validCupTypes.join(', ')}.`);
    }

    if (scale_mode < 0 || scale_mode > 1) {
        throw new Error(`Scale mode must be 0 or 1, but got ${scale_mode}`);
    }
    
    if (tea_pour_N < 0 || tea_pour_N > 7) {
        throw new Error(`Tea pour N must be between 0 and 7, but got ${tea_pour_N}`);
    }

   
    let result = 0;

    if (bypass === 1) 
    {
        result = 1;
    }

    switch (cup_type) {
        case 'Xpod':
            result += 0;
            break;
        case 'Xdripper':
            result += 2;
            break;
        case 'Other':
            result += 4;
            break;
        case 'Tea':
            // result += 4 + 8 + (recipe.pourList.length - 1) * 32;  ????
            break;
        default:
            result += 0;
    }

    if (scale_mode === 0) {
        result += 16;
    } else {
        result += 0;
    }

    return result;
}



/********************
 *   helpers
 *********************/
function get_command_name_by_code(code: number): string | null {
    for (const [name, command] of Object.entries(commands_list)) {
        if (command.code === code) {
            return name;
        }
    }
    return null;
}



function get_command_by_code(code: number): Command | null {
    for (const [name, command] of Object.entries(commands_list)) {
        if (command.code === code) {
            return command;
        }
    }
    return null;
}



function get_command_enum_options(command: string, param_number: number): string[] | null {
    if (!(command in commands_list)) {
        return null;
    }
    
    const param_key = `p${param_number}` as keyof Command;
    const param = commands_list[command][param_key] as CommandParameter | undefined;
    
    if (param && param.type === 'enum' && param.options) {
        return param.options;
    }
    
    return null;
}



function isCommandParam(obj: any): boolean {
    return (
        typeof obj === 'object' &&
        obj !== null &&
        typeof obj.name === 'string' &&
        typeof obj.type === 'string' &&
        typeof obj.min === 'number' &&
        typeof obj.max === 'number' &&
        (obj.options === undefined || (
            Array.isArray(obj.options) && obj.options.every((opt: string) => typeof opt === 'string')
        ))
        // value: не проверяем
    );
}



export {
    make_command,
    commands_list,
    get_command_name_by_code,
    get_command_by_code,
    get_command_enum_options,
}; 