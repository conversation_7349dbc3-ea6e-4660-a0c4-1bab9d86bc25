// Этот скрипт просто сканирует и выводит найденные устройства - 
//  чтобы посмотреть мак адрес и вставить его в код
import ble from './ble';

const SCAN_TIME = 10; // seconds

scan();

async function scan(): Promise<void> {
    await ble.main.init_ble(logs_from_ble);

    console.log("\nscanning for devices: \n");
    await ble.main.scan_all(SCAN_TIME);
    
    await delay(SCAN_TIME * 1000);
    process.exit(0);
}

function logs_from_ble(text: string, color?: string): void {
    if (text.toUpperCase().includes('DC:54:75:C7:E5:D6')) {
        console.log(text);
    } else {
        console.log(text);
    }
}

function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
} 