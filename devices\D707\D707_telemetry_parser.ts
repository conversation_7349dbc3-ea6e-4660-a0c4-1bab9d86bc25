const PACKET_HEADER: number = 0x80;
const STATUS_PACKET_CODE: number = 0x69;

interface TelemetryResult {
    error?: string;
    state?: number;
    mode?: number;
    time?: number;
    left_dorce?: number;
    left_rotation?: number;
    right_force?: number;
    right_rotation?: number;
    front_heat?: number;
    back_heat?: number;
    massage_spot?: number;
    massage_type?: number;
    battery?: number;
    as_string?: string;
}

function parseD707Reply(buffer: Buffer): TelemetryResult {
    const result: TelemetryResult = {};
    
    if (buffer[0] != PACKET_HEADER) {
        result.error = 'packet err: wrong header';
        return result; 
    }

    if(buffer[5] != STATUS_PACKET_CODE) {
        result.error = 'not status packet';
        return result; 
    }

    const offset = 5;

    result.state            = buffer[offset + 1];
    result.mode             = buffer[offset + 2];
    result.time             = buffer[offset + 3] * 255 + buffer[offset + 4];
    result.left_dorce       = buffer[offset + 5];
    result.left_rotation    = buffer[offset + 6];
    result.right_force      = buffer[offset + 7];
    result.right_rotation   = buffer[offset + 8];
    result.front_heat       = buffer[offset + 9];
    result.back_heat        = buffer[offset + 10];
    result.massage_spot     = buffer[offset + 11];
    result.massage_type     = buffer[offset + 12];
    result.battery          = buffer[offset + 13];

    result.as_string = 
                "STATE:" + buffer[offset + 1] +
                " MOD:" + buffer[offset + 2] +
                " TIME:" + (buffer[offset + 3] * 255 + buffer[offset + 4]) +
                " LFCE:" + buffer[offset + 5] +
                " LROT:" + buffer[offset + 6] +
                " RFCE:" + buffer[offset + 7] +
                " RROT:" + buffer[offset + 8] +
                " FHOT:" + buffer[offset + 9] +
                " BHOT:" + buffer[offset + 10] +
                " MARA:" + buffer[offset + 11] +
                " MTYP:" + buffer[offset + 12] +
                " BAT:" + buffer[offset + 13];

    return result;
}

export {
    parseD707Reply
}; 