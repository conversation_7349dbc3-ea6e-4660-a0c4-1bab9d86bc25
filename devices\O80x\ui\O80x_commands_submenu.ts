import ble from '../../../BLE/ble';

import * as commands from '../O80x_commands';
import * as ui from '../../../UI/ui_common_logic';
import * as utils from '../../utils';



interface LineItem {
    text: string;
    action: utils.Command | string | null;
    original_length?: number;
}

interface CommandsSubmenu {
    line_items: LineItem[];
}



const pr = commands.commands_list;

const commands_submenu: CommandsSubmenu = {
    line_items: [
        { text: '← Return',                 action: "return"            }, 
        { text: '',                         action: null                }, 
        { text: '  switch',                 action: pr.SWITCH           },
        { text: '  target temperature',     action: pr.TARGET_TEMP      },
        { text: '  mod',                    action: pr.MOD              },    
        { text: '  brightness',             action: pr.BRIGHTNESS       },
        { text: '  volume',                 action: pr.VOLUME           },
        { text: '  child lock',             action: pr.CHILD_LCK        },
        { text: '  led',                    action: pr.LED              },
        { text: '  open window',            action: pr.OPEN_WINDOW      },
        { text: '',                         action: null                }, 
        { text: '  reset',                  action: pr.RESET            },
        { text: '',                         action: null                }, 
        { text: '  delayed turn off',       action: pr.DELAYED_OFF      },
        { text: '',                         action: null                }, 
        { text: '  sleep mode on',          action: pr.SLEEP_MODE_ON    },
        { text: '  sleep mode start',       action: pr.SLEEP_MOD_START  },
        { text: '  sleep mode stop',        action: pr.SLEEP_MOD_END    },
        { text: '  sleep mode week day',    action: pr.SLEEP_WEEK_DAY   },
        { text: '',                         action: null                }, 
        { text: '  work mode on',           action: pr.WORK_MOD_ON      },
        { text: '  work mode start',        action: pr.WORK_MOD_START   },
        { text: '  work mode stop',         action: pr.WORK_MOD_END     },
        { text: '  work mode week day',     action: pr.WORK_WEEK_DAY    },
        { text: '',                         action: null                }, 
        { text: '  request curr temp',      action: pr.CURR_TEMP        }, 
        { text: '  request power %',        action: pr.POWER            }, 
        { text: '  request all DPs',        action: pr.REQUEST_ALL      }, 
        { text: '  request firmware ver',   action: pr.DEVICE_FIRM_VER  }, 
    ]
};
// save original length or items text
commands_submenu.line_items.forEach(item => {
    item.original_length = item.text.length;
});



let closing_submenu_callback: (() => void) | null = null;



function init(submenu_closed_callback: () => void): void
{
    closing_submenu_callback = submenu_closed_callback;
    init_keys_action();
}



/*****************************
 *    action keys
 ****************************/
function init_keys_action() : void {
    ui.ui_elements.submenu_list.on('select', async function (selected_item: any) 
    {
        const itm = commands_submenu.line_items[selected_item.index - 3];
        if (!itm) throw new Error('err: line item not found');

        // ui.logContent("===".concat(selected_item.index, "   ",selected_item.content));
        switch (itm.action) {
            case "return":
                ui.ui_elements.submenu_list.hide();
                if (closing_submenu_callback) closing_submenu_callback();
                break;
        
            case null:
            // do nothing
            break;

        default:
            {
            const user_input = await utils.ask_user_for_parameters(itm.action as utils.Command);
            if (user_input === null) return;
            (itm.action as utils.Command).p1.value = user_input;

            ble.main.send_data(commands.get_command([itm.action as utils.Command]))
            }
            break;
        }
    });
}



// /************************************
//  *    other functions
//  ***********************************/
function get_submenu_items_list(): string[] {
    return commands_submenu.line_items.map(item => item.text);
}



export { init, get_submenu_items_list }; 