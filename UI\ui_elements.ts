import * as blessed from 'blessed';



const screen = blessed.screen({
    smartCSR: true,
    title: 'BLE control utility',
});



/****************************************
 *   menu box
 *************************************/
const menuBox = blessed.box({
    left: 0,
    top: 0,
    width: '30%',
    height: '100%-2',
    border: {
        type: 'line'
    },
    style: {
        border: {
            fg: 'white'
        }
    },
});

// menu list
const main_menu_list = blessed.list({
    parent: menuBox,
    width: '100%-2',
    height: '100%-2',
    style: {
        selected: {
            bg: 'blue',
            fg: 'white'
        }
    },
    keys: true
});

const submenu_list = blessed.list({
    parent: menuBox,
    width: '100%-2',
    height: '100%-2',
    // items: commandsSubMenuItems,
    style: {
        selected: {
            bg: 'blue',
            fg: 'white'
        },
        border: {
            fg: 'green'
        }
    },
    keys: true,
    hidden: true
});



/****************************************
 *   logs box
 *************************************/
const logsBox = blessed.box({
    left: '30%',
    top: 0,
    tags: true,
    width: '70%',
    height: '100%-2',
    border: {
        type: 'line'
    },
    style: {
        border: {
            fg: 'white'
        }
    },
    scrollable: true,
    alwaysScroll: true,
    scrollbar: {
        ch: ' ',
        track: {
            bg: 'cyan'
        },
        style: {
            inverse: true
        }
    },
    keys: true,
    vi: true,
    mouse: true
});



/****************************************
 *   select popup
 *************************************/
const selectPopupBox = blessed.list({
    top: 'center',
    left: 'center',
    width: '60%',
    height: '60%',
    border: {
      type: 'line'
    },
    style: {
      border: {
        fg: 'green'
      },
      selected: {
        bg: 'yellow',
        fg: 'black'
      }
    },
    keys: true,
    vi: true,
    items: [
      'Popup Item 1',
      'Popup Item 2',
      'Popup Item 3',
      'Popup Item 4',
      'Popup Item 5'
    ],
    tags: true,
    hidden: true,
    label: ' Popup Menu '
  });



/*****************************
 *     help text window
 ***************************/
const helpText = blessed.box({
    bottom: 1,
    left: 'center',
    width: '100%',
    height: 1,
    content: 'TAB: Switch Focus | Arrow Keys: Navigate/Scroll | Enter: Select | Esc: Quit',
    style: {
        fg: 'yellow'
    }
});



/*****************************
 *    status window
 ***************************/
const statusBox = blessed.box({
    bottom: 0,
    left: 0,
    width: '100%',
    height: 1,
    content: 'Device status will be displayed here', 
    style: {
      border: {
        fg: 'white'
      },
      fg: 'white',
      bg: 'blue'
    }
});
  


export { 
    screen,
    menuBox, 
    main_menu_list, 
    submenu_list, 
    logsBox, 
    statusBox,
    selectPopupBox,
    helpText,
} 