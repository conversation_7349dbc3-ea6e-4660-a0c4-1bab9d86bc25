# BLE Module for Node.js

This module provides Bluetooth Low Energy (BLE) functionality for communication with BLE devices.

## Features

- Initialize and manage BLE connections
- Scan for devices by name, MAC address, UUID, service, or manufacturer data
- Connect to devices and manage services/characteristics
- Subscribe to characteristic changes
- Read and write data to characteristics
- Utility functions for handling BLE communication

## Requirements

- Node.js
- `@abandonware/noble` package for BLE communication
- TypeScript (for type safety)

## Usage

### Importing the module

```typescript
import ble from './BLE/ble';
```

### Initialization

```typescript
// Define a logging function
const loggingFunction = (message: string, color?: string) => {
  console.log(message);
};

// Initialize BLE module
await ble.main.init_ble(loggingFunction);
```

### Scanning for devices

```typescript
// Scan for devices by name
const devicesByName = await ble.main.find_all_by_name('MyDeviceName');

// Scan for devices by MAC address
const deviceByMac = await ble.main.find_device_by_mac('XX:XX:XX:XX:XX:XX');

// Scan for devices by service UUID
const devicesByService = await ble.main.find_all_by_service('serviceUuid');

// Scan for devices by manufacturer data
const devicesByManufData = await ble.main.find_all_by_manuf_data('data', 0, 4);

// Scan for devices by UUID
const deviceByUuid = await ble.main.find_device_by_uuid('deviceUuid');
```

### Connecting to a device

```typescript
// Connect to a device (after finding it with one of the search methods)
const services = await ble.main.connect(device);

// Discover services and characteristics
await ble.main.discover_services_and_chars();
```

### Working with characteristics

```typescript
// Set the TX characteristic for sending data
await ble.main.set_TX_char('charUuid');

// Subscribe to a characteristic for receiving data
await ble.main.subscribe_to('charUuid', (data, isNotification) => {
  console.log('Received data:', data);
});

// Send data to the device
const data = Buffer.from([0x01, 0x02, 0x03]);
await ble.main.send_data(data);

// Read data from a characteristic
const readData = await ble.main.read_data_from_char('charUuid');
```

### Disconnecting

```typescript
// Disconnect from the device
await ble.main.disconnect();
```

### Utility functions

```typescript
// Get the current connection status
const status = ble.main.get_connection_status();

// Calculate CRC16 (MODBUS)
const crc = ble.utils.calculate_crc16_modbus(dataBuffer);

// Delay execution
await ble.utils.delay(1000); // Delay for 1 second
```

## TypeScript Types

The module includes TypeScript type definitions for:
- Peripherals
- Services
- Characteristics
- Search parameters
- Events and callbacks

These provide better code completion and type checking when using the module in a TypeScript project. 