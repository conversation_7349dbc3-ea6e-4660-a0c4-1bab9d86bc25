export interface CommandParameter {
    name: string;
    type: 'int' | 'float' | 'enum' | "byte array";
    min?: number;
    max?: number;
    default_value?: number;
    value?: any;    
    options?: string[];                     // возможные значения (если тип = enum)
    option_to_number?: (enm: string) => number;   // для преобразования options в номера, если надо
}



export interface Command {
    code: number;
    params_qty: number;
    menu_item?: boolean;
    read_only?: boolean;
    p1?: CommandParameter;
    p2?: CommandParameter;
    p3?: CommandParameter;
    p4?: CommandParameter;
    p5?: CommandParameter;
    p6?: CommandParameter;
    p7?: CommandParameter;
    p8?: CommandParameter;
    // [key: string]: any;
}



