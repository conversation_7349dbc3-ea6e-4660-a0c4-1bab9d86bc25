import * as blessed from 'blessed';
import { screen } from './ui_elements';



// interfaces
interface SelectItem {
    content: string;
    index: number;
}

interface SelectPopupResult {
    content: string;
    index: number;
}



const mainBox = blessed.box({
    left: 0,
    top: 0,
    width: '100%',
    height: '100%',
    border: {
        type: 'line'
    },
    style: {
        border: {
            fg: 'white'
        }
    }
}) as any;

const selectPopupBox = blessed.list({
    top: 'center',
    left: 'center',
    width: '60%',
    height: '60%',
    border: {
        type: 'line'
    },
    style: {
        border: {
            fg: 'green'
        },
        selected: {
            bg: 'yellow',
            fg: 'black'
        }
    },
    keys: true,
    vi: true,
    items: [
        'Popup Item 1',
        'Popup Item 2',
        'Popup Item 3',
        'Popup Item 4',
        'Popup Item 5'
    ],
    tags: true,
    hidden: true,
    label: ' Popup Menu '
}) as any;

screen.append(mainBox);
screen.append(selectPopupBox);
screen.render();



/**
 * Closes the screen by removing all elements
 */
function close_screen(): void {
    screen.remove(mainBox);
    screen.remove(selectPopupBox);
    screen.render();
}



/**
 * Shows a popup with the given items and returns a promise that resolves 
 * with the selected item when the user makes a selection
 * 
 * @param {string[]} items - Array of items to display in the popup
 * @param {string} [title] - Optional title for the popup
 * @returns {Promise<SelectPopupResult | null>} - The selected item and its index, or null if canceled
 */
function show_select_popup(items: string[], title: string = ' Popup Menu '): Promise<SelectPopupResult | null> {
    return new Promise((resolve) => {
        // Update the popup items
        updatePopupItems(items, title);

        const selectHandler = (item: SelectItem, index: number) => {
            selectPopupBox.removeListener('select', selectHandler);
            selectPopupBox.hide();
            // menuBox.focus();  // focus will be handled from calling place
            screen.render();
            resolve({ content: item.content, index });
        };

        const escapeHandler = () => {
            selectPopupBox.removeListener('select', selectHandler);
            selectPopupBox.removeKey(['escape'], escapeHandler);

            selectPopupBox.hide();
            // menuBox.focus();    // focus will be handled from calling place
            screen.render();

            resolve(null);
        };

        selectPopupBox.on('select', selectHandler);
        selectPopupBox.key(['escape'], escapeHandler);

        selectPopupBox.show();
        selectPopupBox.focus();
        screen.render();
    });
}



/**
 * Function to update popup items before showing the popup
 * @param {string[]} items - Array of items to display in the popup
 * @param {string} [title] - Optional title for the popup
 */
function updatePopupItems(items: string[], title: string = ' Popup Menu '): void {
    selectPopupBox.clearItems();
    items.forEach(item => selectPopupBox.addItem(item));
    selectPopupBox.setLabel(title);
    selectPopupBox.select(0);
    screen.render();
}



export { show_select_popup, close_screen }; 