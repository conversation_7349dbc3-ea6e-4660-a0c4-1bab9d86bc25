import * as fs                  from 'fs';
import * as path                from 'path';

import ble                      from '../../../BLE/ble';

import * as ui                  from '../../../UI/ui_common_logic';
import * as custom_mode_submenu from './D707_custom_mode_submenu';
import * as commands            from '../D707_commands';
import * as telem_parser        from '../D707_telemetry_parser';
import * as ota                 from '../D707_OTA'; 
import * as config              from '../D707_config';
import * as utils               from '../../utils'



const logContent = ui.logContent;

const {
    DEVICE_NAME,
    DEVICE_MAC,
    DEVICE_UUID,
    D707_CONTROL_SERVICE,
    CHAR_ID_TO_SUBSCRIBE_FOR_DATA,
    CHAR_TO_WRITE_COMMANDS_TO,
    D707_OTA_FIRMWARES_PATH,
    D707_PROTOCOL_VERSION
} = config;

const BLE_SCAN_TIME = 10;



interface MenuItem {
    text: string;
    action: (() => Promise<void>) | null;
}

interface MainMenu {
    line_items: MenuItem[];
}



function init(): void {
    ui.init(get_main_menu_items_list(), custom_mode_submenu.get_custom_mode_submenu_items_list(), treat_keys_callback);
    init_keys_action();
    custom_mode_submenu.init(closing_submenu_callback);
    ui.ui_elements.main_menu_list.focus();
}



const main_menu: MainMenu = {
    line_items: [{text: '   --- CONNECT ---   ',                                action: null},
                 {text: 'Connect to D707 by hardcoded MAC (uuid for apple))',   action: connect_by_mac},
                 {text: 'Connect to nearest D707',                              action: connect_nearest},
                 {text: 'Connect - pick from list',                             action: connect_to_device_from_list},
                 {text: 'Disconnect',                                           action: ble.main.disconnect},
                 {text: '',                                                     action: null},
                 {text: '   --- COMMANDS ---   ',                               action: null},
                 {text: 'Unlock',                                               action: unlock},
                 {text: 'Set mode',                                             action: set_mode},
                 {text: 'Custom mode ►',                                        action: custom_mode_submenu_action},
                 {text: 'Get device ID',                                        action: get_device_id},
                 {text: 'OTA',                                                  action: run_ota},
                 {text: '',                                                     action: null},
                 {text: '-- UTILS --',                                          action: null},
                 {text: 'Scan BLE',                                             action: scan_ble},
                 {text: 'Send raw data',                                        action: utils.send_raw_data},
                 {text: 'Clear logs',                                           action: clear_logs},
                 {text: 'Scan services and chars (connect first ^^)',           action: ble.main.discover_services_and_chars},
                 {text: 'Subscribe to data',                                    action: subscribe_to_data}]
};



/*****************************
 *    action keys
 ****************************/
function init_keys_action() : void {
    ui.ui_elements.main_menu_list.on('select', async function (selected_item: { content: string }) {
        const itm = main_menu.line_items.find(line => line.text === selected_item.content);
        if (!itm) {
            throw new Error(`Action not found for item: ${selected_item.content}`);
        }
    
        if (itm.action === null) {
            return;
        }
    
        await itm.action();
    });
}



function treat_keys_callback(active_window: any, key: string): void {
    if((active_window === ui.ui_elements.main_menu_list || active_window === ui.ui_elements.logsBox) && key === 'escape') {
        const conn_status = ble.main.get_connection_status();
        logContent("ble status: ".concat(conn_status));
        if(conn_status === 'connecting') {
            ble.main.stop_scanning();
            logContent("stopping scanning...");
        } else if(conn_status === "connected") {
            ble.main.disconnect();
            logContent("disconnected");
        } else {
            return process.exit(0);
        }
    }
}



/************************************
 *    main menu items actions
 ***********************************/
async function connect_by_mac(): Promise<void> {

    logContent('Поиск D707...');
    await utils.connect_by_mac(DEVICE_MAC, DEVICE_UUID, CHAR_TO_WRITE_COMMANDS_TO, CHAR_ID_TO_SUBSCRIBE_FOR_DATA, data_from_device_cb);
}



async function connect_nearest(): Promise<void> {
    logContent('Поиск ближайшего D707...');
    await utils.connect_to_nearest(DEVICE_NAME, null, CHAR_TO_WRITE_COMMANDS_TO, CHAR_ID_TO_SUBSCRIBE_FOR_DATA, data_from_device_cb)
}



async function connect_to_device_from_list(): Promise<void> {
    logContent('Поиск всех D707...');
    await utils.pick_from_list_and_connect(DEVICE_NAME, null, CHAR_TO_WRITE_COMMANDS_TO, CHAR_ID_TO_SUBSCRIBE_FOR_DATA, data_from_device_cb);
}



async function custom_mode_submenu_action(): Promise<void> {
    ui.ui_elements.main_menu_list.hide();
    ui.ui_elements.submenu_list.show();
    ui.ui_elements.submenu_list.focus();
    ui.ui_elements.screen.render();
}



async function unlock(): Promise<void> {
    ble.main.send_data(commands.get_command_unlock(D707_PROTOCOL_VERSION), CHAR_TO_WRITE_COMMANDS_TO);
}



async function get_device_id(): Promise<void> {
    ble.main.send_data(commands.get_command_device_id(D707_PROTOCOL_VERSION));
}



async function set_mode(): Promise<void> {
    const params = await utils.ask_user_for_parameters(commands.commands_list.SET_MODE);
    if(params === null) return;

    ui.logContent(`setting mode`);
    await send_to_device(commands.get_command_set_mode(params[0], params[1], D707_PROTOCOL_VERSION));
}



async function run_ota(): Promise<void> {
    if (ble.main.get_connection_status() !== 'connected') {
        logContent('Not connected to device.');
        return;
    }

    logContent('OTA started. Reading available firmware files...');

    try {
        // Get list of firmware files in the ota_firmwares folder
        const dirPath = path.join(D707_OTA_FIRMWARES_PATH);
        const files = fs.readdirSync(dirPath);
        const firmwareFiles = files.filter(file => path.extname(file) === '.bin');
        
        if (firmwareFiles.length === 0) {
            logContent('No firmware files found in ' + dirPath);
            return;
        }

        // Display list for the user to select
        const selected = await ui.show_select_popup(firmwareFiles, 'Select firmware to upload');
        if (selected.index < 0) {
            logContent('OTA canceled');
            return;
        }

        const selectedFile = path.join(dirPath, firmwareFiles[selected.index]);
        logContent('Selected firmware: ' + selectedFile);

        // Run OTA update
        await ota.start_OTA(selectedFile, ota_logs_cb);

    } catch (error) {
        logContent('Error in OTA process: ' + error);
    }
}



function ota_logs_cb(text: string): void {
    logContent("    < OTA >    ".concat(text));
}



async function clear_logs(): Promise<void> {
    ui.clear_logs();
}



async function scan_ble(): Promise<void> {
    ble.main.scan_all(BLE_SCAN_TIME);
}



async function subscribe_to_data(): Promise<void> {
    ble.main.subscribe_to(CHAR_ID_TO_SUBSCRIBE_FOR_DATA, data_from_device_cb);
}



/************************************
 *    other functions
 ***********************************/
function get_main_menu_items_list(): string[] {
    return main_menu.line_items.map(item => item.text);
}



function closing_submenu_callback(): void {
    ui.ui_elements.main_menu_list.show();
    ui.ui_elements.main_menu_list.focus();
    ui.ui_elements.screen.render();
}



async function send_to_device(data: Buffer): Promise<void> {
    ble.main.send_data(data);
}



function data_from_device_cb(data: Buffer): void {
    if (data === undefined) {
        return;
    }

    logContent('   <<< incoming data: '. concat(data.toString('hex')));

    const result = telem_parser.parseD707Reply(data);
    if (result.error) 
        {
            if(result.error != 'not status packet')
            {
                logContent('');
            }
                    
            return;
        }
    
        ui.updateStatusBar([result.as_string]);
}



export { init, logContent }; 