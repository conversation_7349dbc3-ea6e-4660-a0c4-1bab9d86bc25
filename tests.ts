import * as assert from 'assert';
import { describe, it } from 'node:test';

// Import using TypeScript syntax (from .ts files)
import * as commands from './devices/C781/c781_commands';
import { get_main_recipes_settings_block, get_cup_type_block, get_recipe_block } from './devices/C781/recipes';
import * as O80xCommands from './devices/O80x/O80x_commands';
import * as O80xParser from './devices/O80x/O80x_telemetry_parser';
import * as deviceReplyParser from './Bork_Smart_Home_BLE/BSH_BLE_replies';

describe('make_command', () => {
    it('should throw an error if params are out of range', () => {
        assert.throws(() => {
            commands.make_command(commands.commands_list.IN_TO_GRINDER_MODE_AND_SET_SIZE_AND_SPEED, -1, 20);
        }, (err: Error) => {
            assert.ok(err.message.includes('param p1 out of range'));
            return true;
        });
        assert.throws(() => {
            commands.make_command(commands.commands_list.IN_TO_GRINDER_MODE_AND_SET_SIZE_AND_SPEED, 10, 65536);
        }, (err: Error) => {
            assert.ok(err.message.includes('param p2 out of range'));
            return true;
        });
    });

    it('should throw an error if wrong number of params', () => {
        assert.throws(() => {
            commands.make_command(commands.commands_list.IN_TO_GRINDER_MODE_AND_SET_SIZE_AND_SPEED, 10);
        }, Error);
        assert.throws(() => {
            commands.make_command(commands.commands_list.IN_TO_GRINDER_MODE_AND_SET_SIZE_AND_SPEED, 10, 20, 30);
        }, (err: Error) => {
            assert.ok(err.message.includes('wrong number of params: should be 2. provided: 10,20,30'));
            return true;
        });
    });

    it('should return correct command string for valid inputs', () => {
        const result = commands.make_command(commands.commands_list.IN_TO_GRINDER_MODE_AND_SET_SIZE_AND_SPEED, 5, 90);        
        const hexString = Buffer.from(result).toString('hex').toUpperCase();
        assert.strictEqual(hexString, '580101461F1400000001050000005A000000FCA0');
    });

    it('should return correct command string for 0 parameters command', () => {
        const result = commands.make_command(commands.commands_list.STOP_GRINDING);
        const hexString = Buffer.from(result).toString('hex').toUpperCase();
        assert.strictEqual(hexString, '580101B10D0C00000001A6BA');
    });

    it('should return correct command string for float parameters command', () => {
        const result = commands.make_command(commands.commands_list.BREWER_START, 2.1, 300, 200, 2, 0);
        const hexString = Buffer.from(result).toString('hex').toUpperCase();
        assert.strictEqual(hexString, '5801019A11200000000166660640000096430000484302000000000000004533');
    });

    it('should find command name by code', () => {
        const commandName = commands.get_command_name_by_code(8006);
        assert.strictEqual(commandName, 'IN_TO_GRINDER_MODE_AND_SET_SIZE_AND_SPEED');
    });

    it('should put together valid "set temperature" command', () => {         // special case: provided value should be * 10
        const result = commands.make_command(commands.commands_list.BREWER_IN, 1, 87);    // 87 converted in float 870!
        const hexString = Buffer.from(result).toString('hex').toUpperCase();
        assert.strictEqual(hexString, '580101471F1400000001010000000080594493FB');
    });
});

// New test for get_main_recipes_settings_block
describe('get_main_recipes_settings_block', () => {
    it('should return the correct buffer for given parameters', () => {
        const bypass_volume = 0; // Example bypass volume
        const bypass_temperature = 0; // Example bypass temperature
        const grind_weight = 12; // Example grind weight

        const expectedBuffer = Buffer.from('580101A61F180000000100000000000000000C0000009DD8', 'hex'); // Expected output buffer

        const result = get_main_recipes_settings_block(bypass_volume, bypass_temperature, grind_weight);
        const resultHex = result.toString('hex').toUpperCase();

        assert.strictEqual(resultHex, expectedBuffer.toString('hex').toUpperCase());
    });
    
    it('should return the correct buffer for given parameters 2', () => {
        const bypass_volume = 200; // Example bypass volume
        const bypass_temperature = 95; // Example bypass temperature
        const grind_weight = 12; // Example grind weight

        const expectedBuffer = Buffer.from('580101A61F1800000001C80000005F0000000C000000EAA4', 'hex'); // Expected output buffer

        const result = get_main_recipes_settings_block(bypass_volume, bypass_temperature, grind_weight);
        const resultHex = result.toString('hex').toUpperCase();

        assert.strictEqual(resultHex, expectedBuffer.toString('hex').toUpperCase());
    });
});

// tests for make_auto_mode_recipe_command (called via make_command)
describe('make_auto_mode_recipe_command', () => {
    const commandType = commands.commands_list.RD_EASYMODE_RECIPE_SEND;
    const validParams = [
        0, // p1: slot N (0-2)
        0, // p2: bypass (0-1)      Байпас воды    0 = Выключен, 1 = Включен
        'Other', // p3: cup type  // Тип чашки/напитка    'Xpod', 'Xdripper', 'Other', 'Tea'
        1, // p4: scale on off (0-1)
        0, // p5: tea pour qty (0-7)
        Buffer.from('20285C0203EF00781E505B0200E5000023505C0200E9000023285B010000000023', 'hex'), // p6: recipe hex (byte array, length 1)
        57, // p7: grind size (0-100)
        16 // p8: ratio (0-1000000000)
    ];
    // Expected Hex: 580101 F62C 2C000000 01 01000000 00000000 02000000 01000000 05000000 AB000000 32000000 803E0000 1E85 (CRC calculated as 0x851E -> 1E85 LE)
    const expectedValidHex = '580102F62C3100000001000420285C0203EF00781E505B0200E5000023505C0200E9000023285B01000000002339A0B9BC';

    it('should generate the correct command buffer for valid parameters', () => {
        const result = commands.make_command(commandType, ...validParams);
        const hexString = Buffer.from(result).toString('hex').toUpperCase();
        assert.strictEqual(hexString, expectedValidHex);
    });

    it('should throw an error for the wrong number of parameters', () => {
        const tooFewParams = validParams.slice(0, 3);
        const tooManyParams = [...validParams, 99];
        assert.throws(() => {
            commands.make_command(commandType, ...tooFewParams);
        }, (err: Error) => {
            assert.strictEqual(err.message, 'Auto mode recipe command RD_EASYMODE_RECIPE_SEND requires 8 parameters, but 3 were provided');
            return true;
        });
         assert.throws(() => {
            commands.make_command(commandType, ...tooManyParams);
        }, (err: Error) => {
            assert.strictEqual(err.message, 'Auto mode recipe command RD_EASYMODE_RECIPE_SEND requires 8 parameters, but 9 were provided');
            return true;
        });
    });

    it('should throw an error for parameters out of range', () => {
        const outOfRangeParams = [...validParams];
        outOfRangeParams[0] = 3; // p1 > 2
        assert.throws(() => {
            commands.make_command(commandType, ...outOfRangeParams);
        }, (err: Error) => {
            assert.ok(err.message.includes('Slot number must be between 0 and 2, but got 3'));
            return true;
        });

        const outOfRangeParams2 = [...validParams];
        outOfRangeParams2[6] = 101; // p7 > 100
         assert.throws(() => {
            commands.make_command(commandType, ...outOfRangeParams2);
        }, (err: Error) => {
            assert.ok(err.message.includes('Grind size must be between 0 and 100, but got 101'));
            return true;
        });
    });
});


// test for get_cup_type_block
describe('get_cup_type_block', () => {
    it('should return the correct buffer for given min and max levels', () => {
        const min_level = -37; // Example min level
        const max_level = 90; // Example max level

        // Expected output buffer
        const expectedBuffer = Buffer.from('580101A81F1400000001000014C20000B4425F0D', 'hex'); // Adjust the expected CRC as needed

        const result = get_cup_type_block(min_level, max_level);
        const resultHex = result.toString('hex').toUpperCase();

        assert.strictEqual(resultHex, expectedBuffer.toString('hex').toUpperCase());
    });
});

// Define interfaces for recipe testing
interface RecipeStep {
    volume: number;
    temperature: number;
    pattern: number;
    vibration: number;
    pause: number;
    grind_speed: number;
    flow_rate: number;
}

interface Recipe {
    steps: RecipeStep[];
    grind_size: number;
    ratio: number;
}

// test for get_recipe_block
describe('get_recipe_block', () => {
    it('should throw an error for invalid recipe object', () => {
        const invalidRecipe: Recipe = {
            steps: [], // No steps provided
            grind_size: NaN,
            ratio: NaN
        };

        assert.throws(() => {
            get_recipe_block(invalidRecipe);
        }, /wrong recipe object/);
    });

    it('should return the correct buffer for a valid recipe object', () => {
        const validRecipe: Recipe = {
            steps: [
                { volume: 134, temperature: 95, pattern: 0, vibration: 0, pause: 5, grind_speed: 90, flow_rate: 3 },
                { volume: 94, temperature: 95, pattern: 0, vibration: 0, pause: 5, grind_speed: NaN, flow_rate: 3 }
            ],
            grind_size: 39,
            ratio: 19
        };
        const expectedBuffer = Buffer.from('580101411F2300000001147F5F0000075F0000FB005A1E5E5F0000FB00001E27BE6B8D', 'hex'); // Adjust the expected output buffer as needed

        const result = get_recipe_block(validRecipe);
        const resultHex = result.toString('hex').toUpperCase();

        assert.strictEqual(resultHex, expectedBuffer.toString('hex').toUpperCase());
    });
});

describe('O80x_get_command_test', () => {
    it('should generate a write command for MOD', () => {
        const comm = O80xCommands.commands_list.MOD;
        comm.p1.value = 2;
        const command = O80xCommands.get_command([comm]);
        assert.deepStrictEqual(command, Buffer.from('f60e01014d4f44000001000002000000', 'hex'));
    });
});

describe('O80x_parseTelemetryPacket_test', () => {
    it('should parse a valid simple telemetry packet', () => {
        const buffer = Buffer.from('01014d4f440000800032000000', 'hex');
        const result = O80xParser.parseTelemetryPacket(buffer);
        assert.deepStrictEqual(result, {
            protocolVersion: 1,
            segmentQty: 1,
            segments: [
                {
                    actor: "MOD",
                    index: 0,
                    capability: 0,
                    actionType: 0x80,   // always same code for reply
                    dataType: 0,
                    data: 50
                }
            ],
            telemStrings: ['MOD: 50  ']
        });
    });

    it('should parse a valid real, big telemetry packet', () => {
        const buffer = Buffer.from('011b535754000080000000000054545000008000000000004d4f4400008000020000004252470000800000000000564f4c00008000000000004c434b00008000000000004c454400008000000000004f5744000080000000000052535400008000000000004552520000800000000000444c4f0000800000000000534c4f0000800000000000534c530000800000000000534c450000800000000000534c570000800000000000574d4f0000800000000000574d530000800000000000574d450000800000000000574d570000800000000000544d500000800000000000505752000080000000000042435400008000000000004446560000800000000000465550000080000000000046565200008000000100004f524e00008000000000004c4f470000800000000000', 'hex');
        const result = O80xParser.parseTelemetryPacket(buffer);
        assert.deepStrictEqual(result, {
            protocolVersion: 1,
            segmentQty: 27,
            segments: [
                {
                    actor: "SWT",
                    index: 0,
                    capability: 0,
                    actionType: 0x80,   // always same code for reply
                    dataType: 0,
                    data: 0
                },
                {
                    actor: "TTP",
                    index: 0,
                    capability: 0,
                    actionType: 0x80,   // always same code for reply
                    dataType: 0,
                    data: 0
                },
                {
                    actor: "MOD",
                    index: 0,
                    capability: 0,
                    actionType: 0x80,   // always same code for reply
                    dataType: 0,
                    data: 2
                },
                {
                  actionType: 128,
                  actor: 'BRG',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'VOL',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'LCK',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'LED',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'OWD',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'RST',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'ERR',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'DLO',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'SLO',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'SLS',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'SLE',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'SLW',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'WMO',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'WMS',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'WME',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'WMW',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'TMP',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'PWR',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'BCT',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'DFV',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'FUP',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'FVR',
                  capability: 0,
                  data: 256,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'ORN',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                },
                {
                  actionType: 128,
                  actor: 'LOG',
                  capability: 0,
                  data: 0,
                  dataType: 0,
                  index: 0
                }
            ],
            telemStrings: ['SWT: 0  TTP: 0  MOD: 2  BRG: 0  VOL: 0  LCK: 0  LED: 0  OWD: 0  RST: 0  ERR: 0  DLO: 0  SLO: 0  SLS: 0  SLE: 0  ',
                'SLW: 0  WMO: 0  WMS: 0  WME: 0  WMW: 0  TMP: 0  PWR: 0  BCT: 0  DFV: 0  FUP: 0  FVR: 256  ORN: 0  LOG: 0  ']
        });
    });
});

describe('bsh device reply parser tests', () => {
    it('should parse a valid device reply', () => {
        const to_parse = Buffer.from([0xAA, 0x41, 0x00, 0x01]);
        const result = deviceReplyParser.parse_device_reply(to_parse);
        assert.deepStrictEqual(result, {
            reply_to: 'set balancer link data',
            reply_msg: 'packet error'
        });
    });
}); 
 