






фильтр для студии, чтобы видеть только отправленные команды:   package:com.xbloom.tbdx    (5801)
                                                                                          (8023)  - от машины. смена экранов
CRC похоже неправильный ??? 


===========
device info    приходит само после подключения
580207 499e 43000000 c14a313541303142353248303335ffffffffffff5631322e30442e3236300030c54701000100640fdc01001d01000000008a5e43000000009c97580207155010000000c100000000


                    16b5




=========================
переход в натройки кликом по картинке машины
ничего не отправляется, ничего не присылается

при обратном переходе:
    command string --------> 58 01 01 56 1F 0C 000000 01
    command string all --------> 580101561F0C00000001C015
    开始发送: 8022 回到首页 
    580101561F0C00000001C015

                -------------------- ответ    
                Received	580207 561F 0C000000 C17A18


=========================
переключение режимов  auto  -  pro
=========================
==========pro:
    command string --------> 580102 F72C 10000000 01000000 00
                             580102 f72c 10000000 01000000 00 2a90
    command string all --------> 580102F72C1000000001000000002A90
    开始发送: 11511 send the recipes order 
                                                    580102F72C1000000001000000002A90
    [VRI[MachinesActivity]#189](f:0,a:1) acquireNextBufferLocked size=1272x360 mFrameNumber=1 applyTransaction=true mTimestamp=2211772358018062(auto) mPendingTransactions.size=0 graphicBufferId=106519483909609 transform=0
    发送成功: 11511 send the recipes order 
                                                    580102F72C1000000001000000002A90
    	Send	580102 F72C 1000000001000000002A90

                ------------ ответ  
                Received	580207F72C10000000C2000000004548
                Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true syncBuffer=false
                Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=true syncBuffer=false
                Received	5802073B9E0C000000C13993
                Received	580207571F10000000C1010000002D33

========== auto
    command string --------> 580102 F72C 10000000 01 9132 7856
    command string all --------> 580102F72C100000000191327856FF58
    开始发送: 11511 send the recipes order 
                                                        580102F72C1000000001 91327856 FF58
                                                        580102f72c1000000001 9132e3f6
    发送成功: 11511 send the recipes order 
                                                        580102F72C100000000191327856FF58
        Send	580102F72C100000000191327856FF58

                ------- ответ    Received	580207 F72C 10000000 C2913278569080
                ------- ответ    Received	580207 571F 10000000 C1410000009A25


=============================================================

переключение С - F

    в F     почему то две отправки, с паузой в 2с
    command string --------> 580101 4A1F 10000000 01000000 00
    command string all --------> 5801014A1F100000000100000000F0AB
    开始发送: 8010 设备显示温度 
                                                        5801014A1F100000000100000000F0AB
    发送成功: 8010 设备显示温度 
                                                        5801014A1F100000000100000000F0AB
        Send	5801014A1F100000000100000000F0AB
    command string --------> 580101561F0C00000001
    command string all --------> 580101561F0C00000001C015
    开始发送: 8022 回到首页 
                                                        580101561F0C00000001C015
    发送成功: 8022 回到首页 
                                                        580101561F0C00000001C015
        Send	580101561F0C00000001C015

                ---------- reply 
                    Received	5802074A1F0C000000C1291C
                    Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true syncBuffer=false
                    Received frameCommittedCallback lastAttemptedDrawFrameNum=2 didProduceBuffer=true syncBuffer=false
                    Received	580207 571F 10000000 C1150000006082
                    Received	580207 561F 0C000000 C17A18
                    Received	580207 3B9E 0C000000 C13993
                    Received	580207 571F 10000000 C1010000002D33


===================================
в С
тоже 2 отправки с паузой в 2с между ними
    command string --------> 580101 4A1F 10000000 01 01000000
    command string all --------> 5801014A1F1000000001010000004BB7
    开始发送: 8010 设备显示温度 
                                                    5801014A1F1000000001010000004BB7
    发送成功: 8010 设备显示温度 
                                                    5801014A1F1000000001010000004BB7
    	Send	5801014A1F1000000001010000004BB7
    command string --------> 580101561F0C00000001
    command string all --------> 580101561F0C00000001C015
    开始发送: 8022 回到首页 
                                                    580101561F0C00000001C015
    发送成功: 8022 回到首页 
                                                    580101561F0C00000001C015
    	Send	580101561F0C00000001C015






=============================================================

переход в режим весов
    command string --------> 580101 431F 0C00000001
    command string all --------> 580101431F0C00000001AA50
    开始发送: 8003 电子秤功能进入指令 
                                                    580101431F0C00000001AA50
    发送成功: 8003 电子秤功能进入指令 
                                                    580101431F0C00000001AA50
    	Send	580101431F0C00000001AA50


=====================================
сброс весов
    command string --------> 58010134210C00000001
    command string all --------> 58010134210C000000018C78
    开始发送: 8500 称重清零 
                                                    58010134210C000000018C78
    发送成功: 8500 称重清零 
                                                    58010134210C000000018C78
    	Send	58010134210C000000018C78

=========================================
переключение единиц веса    (и из меню весов и из меню настроек - одинаково)
в унции
    command string --------> 580101451F100000000102000000
    command string all --------> 580101451F100000000102000000CA8E
    开始发送: 8005 重量单位切换 
                                                    580101451F100000000102000000CA8E
    发送成功: 8005 重量单位切换 
                                                    580101451F100000000102000000CA8E
    	Send	580101451F100000000102000000CA8E
================    в милилитры
    command string --------> 580101451F100000000100000000
    command string all --------> 580101451F100000000100000000BCB7
    开始发送: 8005 重量单位切换 
                                                    580101451F100000000100000000BCB7
    发送成功: 8005 重量单位切换 
                                                    580101451F100000000100000000BCB7
    	Send	580101451F100000000100000000BCB7
===============  в граммы
    command string --------> 580101451F100000000101000000
    command string all --------> 580101451F10000000010100000007AB
    开始发送: 8005 重量单位切换 
                                                    580101451F10000000010100000007AB
    发送成功: 8005 重量单位切换 
                                                    580101451F10000000010100000007AB
    	Send	580101451F10000000010100000007AB

==========================================
переход из весов назад в основное меню
    command string --------> 5801014E1F0C00000001
    command string all --------> 5801014E1F0C00000001E57E
    开始发送: 8014 退出称重页面 
                                                    5801014E1F0C00000001E57E



=======================================================
переход в режим   brewer
    command string --------> 580101 471F 14000000 01 0200000000805944
    command string all --------> 580101471F140000000102000000008059444371
    开始发送: 8007 进入浇水页面 
                                                    580101471F140000000102000000008059444371
    发送成功: 8007 进入浇水页面 
                                                    580101471F140000000102000000008059444371
    	Send	580101 471F 14000000 01 02000000 00805944 4371

=====================
установка объема и flow rate вроде к отправке команд не приводит
установка температуры:
    command string --------> 580101 9E11 1000000001C8000000
    command string all --------> 5801019E111000000001C80000000484
    开始发送: 4510 设定加热温度 
                                                    5801019E111000000001C80000000484
    发送成功: 4510 设定加热温度 
                                                    5801019E111000000001C80000000484
    	Send	580101 9E11 10000000 01 C80000000484
=====================
установка направления
spiral
    command string --------> 580101501F100000000102000000
    command string all --------> 580101501F1000000001020000004BF4
    开始发送: 8016 设置浇水方式 
                                                    580101501F1000000001020000004BF4
    发送成功: 8016 设置浇水方式 
                                                    580101501F1000000001020000004BF4
    	Send	580101501F1000000001020000004BF4
============ circular
    command string --------> 580101501F100000000101000000
    command string all --------> 580101501F10000000010100000086D1
    开始发送: 8016 设置浇水方式 
                                                    580101501F10000000010100000086D1
    发送成功: 8016 设置浇水方式 
                                                    580101501F10000000010100000086D1
    	Send	580101501F10000000010100000086D1
=========== centered
    command string --------> 580101501F100000000100000000
    command string all --------> 580101501F1000000001000000003DCD
    开始发送: 8016 设置浇水方式 
                                                    580101501F1000000001000000003DCD
    发送成功: 8016 设置浇水方式 
                                                    580101501F1000000001000000003DCD
    	Send	580101501F1000000001000000003DCD
======= START
    command string --------> 5801019A112000000001 0000F84100009643000048430000000000000000
                             5801019a112000000001 000084100009643000048430000000000000000 537d    это отсылает утилита.  что то не так
    command string all --------> 580101 9A11 20000000 01 0000F841(31) 00009643(300) 00004843(200) 00000000 00000000 537D
    开始发送: 4506 启动打水 
                                                    5801019A1120000000010000F84100009643000048430000000000000000537D
    发送成功: 4506 启动打水 
                                                    5801019A1120000000010000F84100009643000048430000000000000000537D
    	Send	5801019A1120000000010000F84100009643000048430000000000000000537D
==========stop
    command string --------> 5801019B110C00000001
    command string all --------> 5801019B110C000000013643
    开始发送: 4507 停止打水 
                                                    5801019B110C000000013643
    发送成功: 4507 停止打水 
                                                    5801019B110C000000013643
    	Send	5801019B110C000000013643




============================================
переход в режим   grinder
    command string --------> 580101461F1400000001050000005A000000
    command string all --------> 580101461F1400000001050000005A000000FCA0
    开始发送: 8006 进入磨豆页面 
                                                    580101461F1400000001050000005A000000FCA0
    发送成功: 8006 进入磨豆页面 
                                                    580101461F1400000001050000005A000000FCA0
    	Send	580101461F1400000001050000005A000000FCA0

============================================
grind side change    to 29
    command string --------> 580101461F14000000011D0000003C000000
    command string all --------> 580101461F14000000011D0000003C0000004604
    开始发送: 8006 进入磨豆页面 
                                                    580101461F14000000011D0000003C0000004604
    发送成功: 8006 进入磨豆页面 
                                                    580101461F14000000011D0000003C0000004604
    	Send	580101461F14000000011D0000003C0000004604

============================================
grind speed change  to 90
    command string --------> 580101461F14000000011D0000005A000000
    command string all --------> 580101461F14000000011D0000005A00000038D6
    开始发送: 8006 进入磨豆页面 
                                                    580101461F14000000011D0000005A00000038D6
    发送成功: 8006 进入磨豆页面 
                                                    580101461F14000000011D0000005A00000038D6
    	Send	580101461F14000000011D0000005A00000038D6


===========================================
grind start   (29   90)
    command string --------> 580101AC0D 18000000 01 E8030000 1D000000 5A000000
    command string all --------> 580101AC0D1800000001E80300001D0000005A000000241A
    开始发送: 3500 磨豆挡位调节开始 
                                                    580101AC0D1800000001E80300001D0000005A000000241A
    发送成功: 3500 磨豆挡位调节开始 
                                                    580101AC0D1800000001E80300001D0000005A000000241A
    	Send	580101AC0D1800000001E80300001D0000005A000000241A



===============================================
grind stop
    command string --------> 580101B10D0C00000001
    command string all --------> 580101B10D0C00000001A6BA
    开始发送: 3505 停止磨豆 
                                                    580101B10D0C00000001A6BA
    发送成功: 3505 停止磨豆 
                                                    580101B10D0C00000001A6BA
    	Send	580101B10D0C00000001A6BA


===============================================
переключение источника подачи воды  
трубка
    command string --------> 5801019C11100000000101000000
    command string all --------> 5801019C111000000001010000009CED
    开始发送: 4508 水源切换 
                                                    5801019C111000000001010000009CED
    发送成功: 4508 水源切换 
                                                    5801019C111000000001010000009CED
    	Send	5801019C111000000001010000009CED
    command string --------> 580101561F0C00000001
    command string all --------> 580101561F0C00000001C015
    开始发送: 8022 回到首页 
                                                    580101561F0C00000001C015
    发送成功: 8022 回到首页 
                                                    580101561F0C00000001C015
    	Send	580101561F0C00000001C015

========= бак
    command string --------> 5801019C11100000000100000000
    command string all --------> 5801019C1110000000010000000027F1
    开始发送: 4508 水源切换 
                                                    5801019C1110000000010000000027F1
    发送成功: 4508 水源切换 
                                                    5801019C1110000000010000000027F1
    	Send	5801019C1110000000010000000027F1
    command string --------> 580101561F0C00000001
    command string all --------> 580101561F0C00000001C015
    开始发送: 8022 回到首页 
                                                    580101561F0C00000001C015
    发送成功: 8022 回到首页 
                                                    580101561F0C00000001C015
    	Send	580101561F0C00000001C015


============================================
advanced features
============================================
set pouring radius
при заходе в меню отсылает
    command string --------> 580102F22C0C00000001
    command string all --------> 580102F22C0C0000000155DE
    开始发送: 11506 读取旋转半径 
                                                    580102F22C0C0000000155DE
    发送成功: 11506 读取旋转半径 
                                                    580102F22C0C0000000155DE
    	Send	580102F22C0C0000000155DE

и при сохранении изменений отсылает
    command string --------> 580102 F32C 10000000 01 F8020000
    command string all --------> 580102F32C1000000001F802000034E9
    开始发送: 11507 设置旋转半径 
                                                    580102F32C1000000001F802000034E9
    发送成功: 11507 设置旋转半径 
                                                    580102F32C1000000001F802000034E9
    	Send	580102F32C1000000001F802000034E9

        L1  A8020000       680 
        L2  F8020000       760
        L3  48030000       840
        L4  98030000       920
        L5  E8030000      1000

====================
яркость экрана
    command string --------> 580101 A71F 10000000 01 01000000
    command string all --------> 580101A71F100000000101000000B1BD
    开始发送: 8103 亮度切换 
                                                    580101A71F100000000101000000B1BD
    发送成功: 8103 亮度切换 
                                                    580101A71F100000000101000000B1BD
    	Send	580101A71F100000000101000000B1BD
    command string --------> 580101561F0C00000001
    command string all --------> 580101561F0C00000001C015
    开始发送: 8022 回到首页 
                                                    580101561F0C00000001C015
    发送成功: 8022 回到首页 
                                                    580101561F0C00000001C015
    	Send	580101561F0C00000001C015


==============
амплитуда качания чашки
     при заходе в этот пункт меню телефон отсылает:
    command string --------> 580102F42C0C00000001
    command string all --------> 580102 F42C 0C000000 01 9886
    开始发送: 11508 读取震动pps 
                                                    580102F42C0C000000019886
    发送成功: 11508 读取震动pps 
                                                    580102F42C0C000000019886
    	Send	580102F42C0C000000019886

    при выборе нового значения отсылает:
      command string --------> 580102 F52C 10000000 01 14050000   ()
     command string all --------> 580102F52C100000000114050000F8B3
     开始发送: 11509 写入震动pps 
                      580102F52C100000000114050000F8B3
     发送成功: 11509 写入震动pps 
                  580102F52C100000000114050000F8B3
     	Send	580102F52C100000000114050000F8B3
        

        L1    E8030000    1000
        L2    4C040000    1100
        L3    B0040000    1200
        L4    14050000    1300
        L5    78050000    1400
        L6    DC050000    1500

============================================================================
============================================================================

machine info   ничего не отсылает





============================================================================
синхронизация авторежимов
    OkHttp: {"info":"Operation Successful","result":"success","theCode":"2028580202f400781e5a5a0200f40000233c580200f500002332580100fb00002344a0","theMax":70,"theMin":40}
2025-03-18 14:41:16.626 System.out               I  --->RecipeHex拼接成功：02122028580202f400781e5a5a0200f40000233c580200f500002332580100fb00002344a0
    command string --------> 580102F62C3100000001000220325d0102f500781e415f0200f6000023415e0200f80000233c5d0100fb0000233fa0
    command string all --------> 580102F62C3100000001000220325d0102f500781e415f0200f6000023415e0200f80000233c5d0100fb0000233fa061AE
    开始发送: 11510 send the recipe hex code 
                                                    580102F62C3100000001000220325D0102F500781E415F0200F6000023415E0200F80000233C5D0100FB0000233FA061AE
    发送成功: 11510 send the recipe hex code 
                                                    580102F62C3100000001000220325D0102F500781E415F0200F6000023415E0200F80000233C5D0100FB0000233FA061AE
    	Send	580102F62C3100000001000220325D0102F500781E415F0200F6000023415E0200F80000233C5D0100FB0000233FA061AE
    command string --------> 580102F62C310000000102122028580202f400781e5a5a0200f40000233c580200f500002332580100fb00002344a0
    command string all --------> 580102F62C310000000102122028580202f400781e5a5a0200f40000233c580200f500002332580100fb00002344a02889
    开始发送: 11510 send the recipe hex code 
                                                    580102F62C310000000102122028580202F400781E5A5A0200F40000233C580200F500002332580100FB00002344A02889
    发送成功: 11510 send the recipe hex code 
                                                    580102F62C310000000102122028580202F400781E5A5A0200F40000233C580200F500002332580100FB00002344A02889
    	Send	580102F62C310000000102122028580202F400781E5A5A0200F40000233C580200F500002332580100FB00002344A02889
    command string --------> 580102F62C3100000001011220325c0202f500781e465b0200f5000023415a0200f5000023375a0100fa00002341a0
    command string all --------> 580102F62C3100000001011220325c0202f500781e465b0200f5000023415a0200f5000023375a0100fa00002341a07871
    开始发送: 11510 send the recipe hex code 
                                                    580102F62C3100000001011220325C0202F500781E465B0200F5000023415A0200F5000023375A0100FA00002341A07871
    发送成功: 11510 send the recipe hex code 
                                                    580102F62C3100000001011220325C0202F500781E465B0200F5000023415A0200F5000023375A0100FA00002341A07871
    	Send	580102F62C3100000001011220325C0202F500781E465B0200F5000023415A0200F5000023375A0100FA00002341A07871


================================
read vibration
================================

2025-03-25 08:55:42.004 BLASTBufferQueue         D  [VRI[ProfileActivity]#29](f:0,a:1) acquireNextBufferLocked size=1080x2412 mFrameNumber=1 applyTransaction=true mTimestamp=2447732286580126(auto) mPendingTransactions.size=0 graphicBufferId=131559143244008 transform=0
2025-03-25 08:56:09.559 root                     D  command string --------> 580102F42C0C00000001
2025-03-25 08:56:09.559 root                     D  command string all --------> 580102F42C0C000000019886
2025-03-25 08:56:09.560 ble                      D  开始发送: 11508 读取震动pps 
                                                    580102F42C0C000000019886
2025-03-25 08:56:09.632 ble                      D  发送成功: 11508 读取震动pps 
                                                    580102F42C0C000000019886
2025-03-25 08:56:09.632 ble:68:79:C4:2F:71:0A    D  	Send	580102F42C0C000000019886

================================
set vibration amplitude
================================
2025-03-25 08:58:22.230 root                     D  command string --------> 580102 F52C 10000000 01 14050000
2025-03-25 08:58:22.230 root                     D  command string all --------> 580102F52C100000000114050000F8B3
2025-03-25 08:58:22.230 ble                      D  开始发送: 11509 写入震动pps 
                                                    580102F52C100000000114050000F8B3
2025-03-25 08:58:22.313 ble                      D  发送成功: 11509 写入震动pps 
                                                    580102F52C100000000114050000F8B3
2025-03-25 08:58:22.313 ble:68:79:C4:2F:71:0A    D  	Send	580102F52C100000000114050000F8B3





        ======================================================================
        ======================================================================
        ======================================================================
        ======================================================================

        запуск рецепта


2025-03-25 08:46:05.648 root                     D  command string --------> 580101A61F180000000100000000000000000F000000
2025-03-25 08:46:05.648 root                     D  command string all --------> 580101A61F180000000100000000000000000F00000050FD
2025-03-25 08:46:05.648 ble                      D  开始发送(start sending): 8102 Bypass 
                                                    580101A61F180000000100000000000000000F00000050FD
2025-03-25 08:46:05.667 ble                      D  发送成功(sent): 8102 Bypass 
                                                    580101A61F180000000100000000000000000F00000050FD
2025-03-25 08:46:05.667 ble:68:79:C4:2F:71:0A    D  	Send	580101A61F180000000100000000000000000F00000050FD

2025-03-25 08:46:06.056 root                     D  command string --------> 580101A81F140000000100008C4200002042
2025-03-25 08:46:06.057 root                     D  command string all --------> 580101A81F140000000100008C42000020427D9F
2025-03-25 08:46:06.058 ble                      D  开始发送: 8104 设置胶囊杯类型 (set capsule cup type)
                                                    580101 A81F 14000000 01 00008C42 00002042 7D9F    (values 70 and 40 in float)
2025-03-25 08:46:06.071 ble                      D  发送成功: 8104 设置胶囊杯类型 (set capsule cup type)
                                                    580101A81F140000000100008C42000020427D9F
2025-03-25 08:46:06.072 ble:68:79:C4:2F:71:0A    D  	Send	580101A81F140000000100008C42000020427D9F

2025-03-25 08:46:06.457 root                     D  command string --------> 580101 411F 37000000 01 283c5d0202f100781e415d0002f40000233c5d0100f4000023285d0000fb0000231e550000fb00002341aa
2025-03-25 08:46:06.458 root                     D  command string all --------> 580101411F3700000001283c5d0202f100781e415d0002f40000233c5d0100f4000023285d0000fb0000231e550000fb00002341aaD5CA
2025-03-25 08:46:06.459 ble                      D  开始发送: 8001 发送配方 (send recipe)
                                                    580101 411F 37000000 01 28-3C-5D-02(head_data) 02F10078 1E415D00 02F40000 233C5D01 00F40000 23285D00 00FB0000231E550000FB000023 41AA()  D5CA(checksum)
2025-03-25 08:46:06.479 ble                      D  发送成功: 8001 发送配方 (send recipe)
                                                    580101411F3700000001283C5D0202F100781E415D0002F40000233C5D0100F4000023285D0000FB0000231E550000FB00002341AAD5CA
2025-03-25 08:46:06.480 ble:68:79:C4:2F:71:0A    D  	Send	580101411F3700000001283C5D0202F100781E415D0002F40000233C5D0100F4000023285D0000FB0000231E550000FB00002341AAD5CA

2025-03-25 08:46:16.661 root                     D  command string --------> 580101421F0C00000001
2025-03-25 08:46:16.661 root                     D  command string all --------> 580101421F0C000000017FCF
2025-03-25 08:46:16.661 ble                      D  开始发送: 8002 执行配方 (Execute)
                                                    580101421F0C000000017FCF
2025-03-25 08:46:16.673 ble                      D  发送成功: 8002 执行配方 (Execute)
                                                    580101421F0C000000017FCF
2025-03-25 08:46:16.673 ble:68:79:C4:2F:71:0A    D  	Send	580101421F0C000000017FCF
2025-03-25 08:47:02.603 ble                      D  当前重量：20501 32.336
                                                    580207155010000000C110580142584B
2025-03-25 08:47:43.790 Quality                  I  Skipped: true 1 cost 18.653587 refreshRate 16580189 bit true processName com.xbloom.tbdx
2025-03-25 08:48:22.620 Quality                  I  Skipped: true 6 cost 101.180534 refreshRate 16580171 bit true processName com.xbloom.tbdx
2025-03-25 08:48:41.849 Quality                  I  Skipped: true 1 cost 20.062803 refreshRate 16580177 bit true processName com.xbloom.tbdx

cup type
omni dripper 580101 A81F 14000000 01 0000DC42 0000B442 21A1    110 - 90
xPod         580101 A81F 14000000 01 00008C42 00002042 7D9F    70 - 40
other        580101 A81F 14000000 01 00004843 0000A042 2A0F    200 - 80


код рецепта
08 785F0203FB005A1E27BE
14 7F5F0000075F0000FB005A1E 5E5F0000FB00001E 27BE
14(длина не учитывая  2 байта в конце) 
   7F(пролив 127) 
     5F(95 темп) 
       0000(паттерн и вибрация (1 до 2 после 3 и то и то)) 
           --- дополнительный блок из за того что объем больше 127
           07(пролив) 
             5F(темп) 
               0000(паттерн и вибрация) 
               --- конец дополнительного блока
                   FB(pause)
                     00
                       5A (grind speed)
                         1E(flow rate * 10)    

                            5E(94пролив) 
                              5F(темп) 
                                0000(паттерн и вибрация) 
                                    FB (pause)
                                      00 
                                        00 grind speed
                                          1E(flow rate * 10)      
                                             27(39 - помол) 
                                               BE(190 - ratio * 10)


***********************************
       калибровка весов 
***********************************
с мобилки никаких команд не отправляется вообще. имеющиеся там экраны это прото хелп. да и то, когда начинаешь калибровку поверх хелпа выскакивает экран весов.     все действия происходят на самой машине.  заходишь в режим весов. жмешь три раза среднюю кнопку, появляется экран "калибровка весов".  надо нажать правую кнопку чтобы начать.  нажимаешь, машина пишет 0г.   жмешь правую кнопку. пишет 100г , ставишь 100г и жмешь правую кнопку.  пишет 500г,   ставишь и жмешь. пишет done.   и когда жмешь кнопку на секунду показывается значек песочных часов. типа думает

по номерам экранов это выглядит вот так - в скобках у номеров подписал какие это экраны                         2025-03-31 09:50:29.634 root                     D  startingAddressValue value: 8023
2025-03-31 09:50:29.635 ble                      D  Machine Activity：8023 4      ( переход в режим весов )
                                                    580207571F10000000C1040000007A5D
2025-03-31 09:50:30.828 root                     D  startingAddressValue value: 8023 
2025-03-31 09:50:30.829 ble                      D  Machine Activity：8023 5     (режим весов) 
                                                    580207571F10000000C105000000C141                                2025-03-31 09:45:35.606 root                     D  startingAddressValue value: 8023
2025-03-31 09:45:35.607 ble                      D  Machine Activity：8023 57            (scal cal)
                                                    580207571F10000000C139000000079A
2025-03-31 09:45:39.040 root                     D  startingAddressValue value: 8023
2025-03-31 09:45:39.042 ble                      D  Machine Activity：8023 58      (no load)
                                                    580207571F10000000C13A000000CABF
2025-03-31 09:45:44.677 root                     D  startingAddressValue value: 8023
2025-03-31 09:45:44.679 ble                      D  Machine Activity：8023 59             (песочные часы)
                                                    580207571F10000000C13B00000071A3
2025-03-31 09:45:49.142 root                     D  startingAddressValue value: 8023
2025-03-31 09:45:49.144 ble                      D  Machine Activity：8023 63          (100 г)
                                                    580207571F10000000C13F0000009DD1
2025-03-31 09:46:00.259 root                     D  startingAddressValue value: 8023
2025-03-31 09:46:00.260 ble                      D  Machine Activity：8023 59         (песочные часы)
                                                    580207571F10000000C13B00000071A3
2025-03-31 09:46:04.750 root                     D  startingAddressValue value: 8023
2025-03-31 09:46:04.751 ble                      D  Machine Activity：8023 60           (500г)
                                                    580207571F10000000C13C00000050F4
2025-03-31 09:46:15.686 root                     D  startingAddressValue value: 8023
2025-03-31 09:46:15.688 ble                      D  Machine Activity：8023 59          (песочные часы)
                                                    580207571F10000000C13B00000071A3
2025-03-31 09:46:20.174 root                     D  startingAddressValue value: 8023
2025-03-31 09:46:20.175 ble                      D  Machine Activity：8023 37           (done)
                                                    580207571F10000000C12500000092CE
2025-03-31 09:46:23.650 root                     D  startingAddressValue value: 8023
2025-03-31 09:46:23.652 ble                      D  Machine Activity：8023 1           (основной экран)
                                                    580207571F10000000C1010000002D33



***********************************
       калибровка гриндера
***********************************
2025-03-31 10:13:17.432 root                     D  command string --------> 580101AE0D1000000001E8030000
2025-03-31 10:13:17.432 root                     D  command string all --------> 580101AE0D1000000001E80300001B7C
2025-03-31 10:13:17.433 ble                      D  开始发送: 3502 磨豆档位归0 
                                                    580101AE0D1000000001E80300001B7C
2025-03-31 10:13:17.503 ble                      D  发送成功: 3502 磨豆档位归0 
                                                    580101AE0D1000000001E80300001B7C
2025-03-31 10:13:17.503 ble:68:79:C4:2F:71:0A    D  	Send	580101AE0D1000000001E80300001B7C



***********************************
       установка рецептов авторежима
***********************************
command string -------->     580102 F62C 31000000 01 0004 20285c0203ef00781e505b0200e5000023505c0200e9000023285b01000000002339a0
                                                          20 - длина не учитывая  2 байта в коце
                                                            28 - объем
                                                              5с - температура
                                                                02   паттерн
                                                                  03   вибрация
                                                                    ef  пауза
                                                                      00
                                                                        78 (120)  RPM 
                                                                          1e  (30)  flow rate * 10

                                                                            50 объем
                                                                              5b  температура
                                                                                02    паттерн
                                                                                  00   вибрация
                                                                                     e5 пауза 
                                                                                       000023505c0200e9000023285b010000000023
                                                                                                                             39 помол 
                                                                                                                               a0 160 ratio * 10
command string all --------> 580102 F62C 31000000 01 0004 20285c0203ef00781e505b0200e5000023505c0200e9000023285b01000000002339a0B9BC
开始发送: 11510 send the recipe hex code 
580102 F62C3100000001000420285C0203EF00781E505B0200E5000023505C0200E9000023285B01000000002339A0B9BC
发送成功: 11510 send the recipe hex code 
580102 F62C3100000001000420285C0203EF00781E505B0200E5000023505C0200E9000023285B01000000002339A0B9BC
	Send	580102 F62C3100000001000420285C0203EF00781E505B0200E5000023505C0200E9000023285B01000000002339A0B9BC

command string --------> 580102 F62C 31000000 01 0112 20325c0202f500781e465b0200f5000023415a0200f5000023375a0100fa00002341a0
command string all --------> 580102 F62C31000000 01 011220325c0202f500781e465b0200f5000023415a0200f5000023375a0100fa00002341a07871
开始发送: 11510 send the recipe hex code 
580102 F62C31000000 01 011220325C0202F500781E465B0200F5000023415A0200F5000023375A0100FA00002341A07871
发送成功: 11510 send the recipe hex code 
580102 F62C31000000 01 011220325C0202F500781E465B0200F5000023415A0200F5000023375A0100FA00002341A07871
	Send	580102 F62C31000000 01 011220325C0202F500781E465B0200F5000023415A0200F5000023375A0100FA00002341A07871
    
command string --------> 580102 F62C 31000000 01 0212 2028580202f400781e5a5a0200f40000233c580200f500002332580100fb00002344a0
command string all --------> 580102 F62C31000000 01 02122028580202f400781e5a5a0200f40000233c580200f500002332580100fb00002344a02889
开始发送: 11510 send the recipe hex code 
580102 F62C31000000 01 02122028580202F400781E5A5A0200F40000233C580200F500002332580100FB00002344A02889
发送成功: 11510 send the recipe hex code 
580102 F62C31000000 01 02122028580202F400781E5A5A0200F40000233C580200F500002332580100FB00002344A02889
	Send	580102 F62C31000000 01 02122028580202F400781E5A5A0200F40000233C580200F500002332580100FB00002344A02889


580207f62c0c000000c2d204
580207f62c0c000000c2d204






>> raw incoming data: 580207399e10000000c154000000ef60                                                                               
>> incoming block name: null code: 40505                                                                                             
>> telemetry parsed: Command Code: 40505  Data: C154000000  CRC: EF60  CRC: ok Error: Unknown command code: 40505                    

>> raw incoming data: 5802074e9e10000000c1550000007882                                                                               
>> incoming block name: RD_CurrentGrinder code: 40526                                                                                
>> telemetry parsed: Command Code: 40526  Data: C155000000  CRC: 7882  CRC: ok                                                       
    Values: grinder calibration done = 85                                                                                                              

>> raw incoming data: 580207571f10000000c12500000092ce                                                                               
>> incoming block name: U_PAGE_NUMBER code: 8023                                                                                     
>> telemetry parsed: Command Code: 8023  Data: C125000000  CRC: 92CE  CRC: ok                                                        
    Values: pageNumber = 37  











                                                                                                         │ 
setting water source to 0                                                                                            │ 
< BLE >    sending: 5801019c1110000000010000000027f1                                                               │ 
>> raw incoming data: 5802079c1110000000c1000000006ab3                                                             │ 
>> incoming block name: BREWER_WATER_SOURCE code: 4508                                                             │ 
>> telemetry parsed: Command Code: 4508  Data: C100000000  CRC: 6AB3  CRC: ok                                      │ 
    Values: source = 0                                                                                             │ 
                                                                                                                   │ 
                                                                                                                   │ 
>> raw incoming data: 580207571f10000000c1190000005415                                                             │ 
>> incoming block name: U_PAGE_NUMBER code: 8023                                                                   │ 
>> telemetry parsed: Command Code: 8023  Data: C119000000  CRC: 5415  CRC: ok                                      │ 
    Values: pageNumber = 25      






    >> raw incoming data: 5802074f1f18000000c1000000000100000001000000252c                                             │ 
>> incoming block name: RD_UNIT_CHANGE code: 8015                                                                  │ 
>> telemetry parsed: Command Code: 8015  Data: C1000000000100000001000000  CRC: 252C  CRC: ok                      │ 
    Values: weight units = 0temperature units = 1water inle mode = 1                                               │ 
                                                                                                                   │ 
                                                                                                                   │ 
>> raw incoming data: 580207571f10000000c1010000002d33                                                             │ 
>> incoming block name: U_PAGE_NUMBER code: 8023                                                                   │ 
>> telemetry parsed: Command Code: 8023  Data: C101000000  CRC: 2D33  CRC: ok                                      │ 
    Values: pageNumber = 1   





        APP_GRINDER_IN                                                                                                                      │ 
< BLE >    sending: 580101461f0c0000000109a0                                                                       
>> raw incoming data: 580207461f0c000000c1b3ad                                                                     
>> incoming block name: IN_TO_GRINDER_MODE_AND_SET_SIZE_AND_SPEED code: 8006                                       
>> telemetry parsed: Command Code: 8006  Data: C1  CRC: B3AD  CRC: ok  

       RD_BREWER_IN 
< BLE >    sending: 580101471f0c000000 01 dc3f                                                                       │ 
>> raw incoming data: 580207471f0c000000c16632                                                                     │ 
>> incoming block name: BREWER_IN code: 8007                                                                       │ 
>> telemetry parsed: Command Code: 8007  Data: C1  CRC: 6632  CRC: ok    

         RD_IN_SCALE
< BLE >    sending: 580101431f0c00000001aa50                                                                       
>> raw incoming data: 580207431f0c000000c1105d                                                                     
>> incoming block name: SCALE_ENTER code: 8003                                                                     
>> telemetry parsed: Command Code: 8003  Data: C1  CRC: 105D  CRC: ok 
                                                                                                    