/**
* === MQTT protocol packet structure ===
 * 
 * -byteN:   -data:
 * 1:       protocol version 0x01
 * 2:       qty of commands  
 * 3,4,5:   actor: FAN ect.
 * 6:       index: needed in case of multiple actors of same type
 * 7:       capability: if multiple capabilities of single actor. like cw/ccw rotation   
 * 8:       command type: write (1) or read(0).   read packet ends here
 * 
 * if its write command, more bytes:
 * 9:       CHANGE_TYPE: absolute (0) or increment (1)
 * 10:      DATA_TYPE: 0 - int32 , 1 - float   
 * 11,12,13,14:  data.   int - little endian: 3 = 0x03 0x00 0x00 0x00
 * 
 * 9 or 15:       next actor, if many of them in same packet. 9 if prev actor was read, 15 if it was write
 * 
 * for example: 01 01 46 56 52 00 00 00    read firmware version
 * 
 * 
 * 
 * Reply/status change packet structure:
 * 1:       protocol version 0x01
 * 2:       qty of segments
 * 3,4,5:   actor
 * 6:       index: if multiple actors of same type
 * 7:       capability 
 * 8:       action type - always 0x80 here
 * 9:       DATA_TYPE
 * 10,11,12,13:      data
 * 
 * 
 * 
 * Firmware update command (FUP) has one extra field: string with link to firmware.
 * 1:       protocol version 0x01
 * 2:       qty of commands
 * 3,4,5:   actor: FUP 
 * 6:       index: always 0 
 * 7:       capability: 0
 * 8:       command type: read(0) / write (1) 
 * since its write command
 * 9:       CHANGE_TYPE: absolute: 0
 * 10:      DATA_TYPE: 0   
 * 11,12,13,14: int: url link length 
 * 15...    url link to firmware
 *
 * for example
 * 01 01 46 55 50 00 00 01 00 00 3E 00 00 00 68 74 74 70 3a 2f 2f 73 74 2e 62 6f 72 6b 2e 72 75 2f 61 70 70 6c 69 63 61 74 69 6f 6e 2f 66 69 72 6d 77 61 72 65 73 2f 77 69 66 69 2f 61 38 33 30 2f 66 69 72 6d 77 61 72 65 2e 62 69 6e
 */
import { Command} from '../CommandTypes/command_types';


const MQTT_COMMAND_VIA_BLE_CODE = 0xF6;
const PROTOCOL_VERSION = 1;



interface CommandsList {
    [key: string]: Command;
}

const commands_list: CommandsList = {
    SWITCH: {                                      
        code: 1,
        params_qty: 1,
        p1: {name: "SWT", type: "enum", min: 0, max: 1, options: ['off','on']},
    },

    TARGET_TEMP: {
        code: 2,
        params_qty: 1,
        p1: {name: "TTP", type: "int", min: 7, max: 28},
    },

    MOD: {
        code: 3,
        params_qty: 1,
        p1: {name: "MOD", type: "enum", min: 0, max: 3, options: ['comf','auto','sleep','save']},
    },

    BRIGHTNESS: {
        code: 4,
        params_qty: 1,
        p1: {name: "BRG", type: "enum", min: 0, max: 2, options: ['1','2','3']},
    },

    VOLUME: {
        code: 5,
        params_qty: 1,
        p1: {name: "VOL", type: "enum", min: 0, max: 3, options: ['1','2','3','4']},
    },

    CHILD_LCK: {
        code: 6,
        params_qty: 1,
        p1: {name: "LCK", type: "enum", min: 0, max: 1, options: ['off','on']},
    },

    LED: {
        code: 7,
        params_qty: 1,
        p1: {name: "LED", type: "enum", min: 0, max: 1, options: ['off','on']},
    },

    OPEN_WINDOW: {
        code: 8,
        params_qty: 1,
        p1: {name: "OWD", type: "enum", min: 0, max: 1, options: ['off','on']},
    },

    RESET: {
        code: 9,
        params_qty: 1,
        p1: {name: "RST", type: "enum", min: 0, max: 1, options: ['off','on']},
    },

    ERR: {
        code: 15,
        params_qty: 1,
        p1: {name: "ERR", type: "enum", min: 0, max: 4, options: ['', 'Temperature sensor error','not in vertical position','','Remote control sensor error']},
        read_only: true
    },



    DELAYED_OFF: {
        code: 20,
        params_qty: 1,
        p1: {name: "DLO", type: "int", min: 0, max: 24*60},   // minutes
    },

    // Sleep mode
    SLEEP_MODE_ON: {
        code: 21,
        params_qty: 1,
        p1: {name: "SLO", type: "enum", min: 0, max: 1, options: ['off','on']},

    },

    SLEEP_MOD_START: {
        code: 22,
        params_qty: 1,
        p1: {name: "SLS", type: "int", min: 0, max: 24*60},   // minutes
    },

    SLEEP_MOD_END: {
        code: 23,
        params_qty: 1,
        p1: {name: "SLE", type: "int", min: 0, max: 24*60},   // minutes
    },

    SLEEP_WEEK_DAY: {
        code: 24,
        params_qty: 1,
        p1: {name: "SLW",  type: "enum", min: 0, max: 6, options: ['Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'],
                option_to_number : function(enm: string): number { 
                    switch (enm) {
                        case 'Monday': return 1;
                        case 'Tuesday': return 2;
                        case 'Wednesday': return 4;
                        case 'Thursday': return 8;
                        case 'Friday': return 16;
                        case 'Saturday': return 32;
                        case 'Sunday': return 64;
                        default: return 0;
                    }
                },
        },
    },

    WORK_MOD_ON: {
        code: 25,
        params_qty: 1,
        p1: {name: "WMO", type: "enum", min: 0, max: 1, options: ['off','on']},
    },

    WORK_MOD_START: {
        code: 26,
        params_qty: 1,
        p1: {name: "WMS", type: "int", min: 0, max: 24*60},   // minutes
    },

    WORK_MOD_END: {
        code: 27,
        params_qty: 1,
        p1: {name: "WME", type: "int", min: 0, max: 24*60},   // minutes
    },

    WORK_WEEK_DAY: {
        code: 28,
        params_qty: 1,
        p1: {name: "WMW", type: "enum", min: 0, max: 6, options: ['Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'],
                option_to_number : function(enm: string): number { 
                    switch (enm) {
                        case 'Monday': return 1;
                        case 'Tuesday': return 2;
                        case 'Wednesday': return 4;
                        case 'Thursday': return 8;
                        case 'Friday': return 16;
                        case 'Saturday': return 32;
                        case 'Sunday': return 64;
                        default: return 0;
                    }
                },
        },
    },

    CURR_TEMP: {
        code: 30,
        params_qty: 1,
        p1: {name: "TMP",  type: "int", min: 0, max: 60},  
        read_only: true
    },

    POWER: {
        code: 31,
        params_qty: 1,
        p1: {name: "PWR", type: "int", min: 0, max: 100},  
        read_only: true
    },

    REQUEST_ALL: {
        code: 0xff,
        params_qty: 1,
        p1: {name: "BCT", type: "enum", min: 0, max: 1, options: ['off','on']},
    },

    DEVICE_FIRM_VER: {
        code: 0xfe,
        params_qty: 1,
        p1: {name: "DFV", type: "int", min: 1, max: 1},
        read_only: true
    }
};



/**
 * Returns buffer with raw command to be sent to device
 * Expects each command to have 'value' if its write command. 
 * 
 * @param {Command[]} params  array of commands from commands_list
 */
function get_command(params: Command[]): Buffer {
    const command: number[] = [];
    command.push(MQTT_COMMAND_VIA_BLE_CODE);
    command.push(0);    // will place packet length here - later
    command.push(PROTOCOL_VERSION);
    command.push(params.length);

    params.forEach(element => {        
        command.push(element.p1.name.charCodeAt(0));
        command.push(element.p1.name.charCodeAt(1));
        command.push(element.p1.name.charCodeAt(2));

        command.push(0);   // index. always 0
        command.push(0);   // capability. always 0

        
        if(element.p1.value != undefined)  // write command
        {
            if(element.read_only)
            {
                throw new Error("attempt to write read-only actor");
            }

            command.push(1);    // indicate write command
            command.push(0);    // indicate absolue value. increment - TODO
            command.push(0);    // indicate int value.  float is not used in O80x protocol

            // push 4 bytes of value in little endian
            const value = element.p1.value;
            command.push(value & 0xFF);
            command.push((value >> 8) & 0xFF);
            command.push((value >> 16) & 0xFF);
            command.push((value >> 24) & 0xFF);
        } else {
            command.push(0);    // indicate read command
        }
    });

    command[1] = command.length - 2;    // update packet length

    return Buffer.from(command);
}

export {
    commands_list,
    get_command
} 